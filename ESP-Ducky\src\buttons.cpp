/**
 * ESP-<PERSON><PERSON>ton Management Implementation
 * 
 * Author: <PERSON> - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#include "buttons.h"

// Global instance
ButtonManager buttonManager;

ButtonManager::ButtonManager() {
    upPin = BUTTON_UP_PIN;
    downPin = BUTTON_DOWN_PIN;
    okPin = BUTTON_OK_PIN;
    
    upPressed = false;
    downPressed = false;
    okPressed = false;
    
    upPrevious = false;
    downPrevious = false;
    okPrevious = false;
    
    upLastDebounce = 0;
    downLastDebounce = 0;
    okLastDebounce = 0;
    
    upPressStart = 0;
    downPressStart = 0;
    okPressStart = 0;
    
    upLongPressed = false;
    downLongPressed = false;
    okLongPressed = false;
}

void ButtonManager::begin() {
    DEBUG_PRINTLN("[Buttons] Initializing buttons...");
    
    // Configure button pins as inputs with pull-up resistors
    pinMode(upPin, INPUT_PULLUP);
    pinMode(downPin, INPUT_PULLUP);
    pinMode(okPin, INPUT_PULLUP);
    
    // Read initial states
    upPressed = !digitalRead(upPin);
    downPressed = !digitalRead(downPin);
    okPressed = !digitalRead(okPin);
    
    upPrevious = upPressed;
    downPrevious = downPressed;
    okPrevious = okPressed;
    
    DEBUG_PRINTF("[Buttons] Button pins configured: UP=%d, DOWN=%d, OK=%d\n", upPin, downPin, okPin);
    DEBUG_PRINTLN("[Buttons] Buttons initialized successfully");
}

void ButtonManager::update() {
    unsigned long currentTime = millis();
    
    // Process each button
    processButton(BUTTON_UP, upPin, upPressed, upPrevious, upLastDebounce, upPressStart, upLongPressed);
    processButton(BUTTON_DOWN, downPin, downPressed, downPrevious, downLastDebounce, downPressStart, downLongPressed);
    processButton(BUTTON_OK, okPin, okPressed, okPrevious, okLastDebounce, okPressStart, okLongPressed);
}

void ButtonManager::processButton(ButtonType button, int pin, bool& pressed, bool& previous, 
                                 unsigned long& lastDebounce, unsigned long& pressStart, bool& longPressed) {
    unsigned long currentTime = millis();
    bool reading = !digitalRead(pin);  // Inverted because of pull-up
    
    // Debounce logic
    if (reading != previous) {
        lastDebounce = currentTime;
    }
    
    if ((currentTime - lastDebounce) > BUTTON_DEBOUNCE_DELAY) {
        if (reading != pressed) {
            pressed = reading;
            
            if (pressed) {
                // Button pressed
                pressStart = currentTime;
                longPressed = false;
                addEvent(button, BUTTON_PRESSED);
            } else {
                // Button released
                unsigned long pressDuration = currentTime - pressStart;
                addEvent(button, BUTTON_RELEASED, pressDuration);
            }
        }
        
        // Check for long press
        if (pressed && !longPressed && (currentTime - pressStart) >= BUTTON_LONG_PRESS_TIME) {
            longPressed = true;
            addEvent(button, BUTTON_LONG_PRESS, currentTime - pressStart);
        }
    }
    
    previous = reading;
}

void ButtonManager::addEvent(ButtonType button, ButtonState state, unsigned long duration) {
    ButtonEvent event;
    event.button = button;
    event.state = state;
    event.timestamp = millis();
    event.duration = duration;
    
    eventQueue.push_back(event);
    
    // Limit queue size
    if (eventQueue.size() > 10) {
        eventQueue.erase(eventQueue.begin());
    }
    
    DEBUG_PRINTF("[Buttons] Event: %s %s (duration: %lu ms)\n", 
                buttonTypeToString(button).c_str(), 
                buttonStateToString(state).c_str(), 
                duration);
}

bool ButtonManager::hasEvent() {
    return !eventQueue.empty();
}

ButtonEvent ButtonManager::getEvent() {
    if (eventQueue.empty()) {
        ButtonEvent emptyEvent = {BUTTON_UP, BUTTON_IDLE, 0, 0};
        return emptyEvent;
    }
    
    ButtonEvent event = eventQueue.front();
    eventQueue.erase(eventQueue.begin());
    return event;
}

void ButtonManager::clearEvents() {
    eventQueue.clear();
}

bool ButtonManager::isUpPressed() {
    return upPressed;
}

bool ButtonManager::isDownPressed() {
    return downPressed;
}

bool ButtonManager::isOKPressed() {
    return okPressed;
}

bool ButtonManager::isAnyPressed() {
    return upPressed || downPressed || okPressed;
}

bool ButtonManager::isUpDownPressed() {
    return upPressed && downPressed;
}

bool ButtonManager::isOKLongPressed() {
    return okPressed && okLongPressed;
}

String ButtonManager::buttonTypeToString(ButtonType button) {
    switch (button) {
        case BUTTON_UP: return "UP";
        case BUTTON_DOWN: return "DOWN";
        case BUTTON_OK: return "OK";
        default: return "UNKNOWN";
    }
}

String ButtonManager::buttonStateToString(ButtonState state) {
    switch (state) {
        case BUTTON_IDLE: return "IDLE";
        case BUTTON_PRESSED: return "PRESSED";
        case BUTTON_RELEASED: return "RELEASED";
        case BUTTON_LONG_PRESS: return "LONG_PRESS";
        default: return "UNKNOWN";
    }
}

void ButtonManager::printEvent(ButtonEvent event) {
    DEBUG_PRINTF("[Buttons] %s %s at %lu ms (duration: %lu ms)\n",
                buttonTypeToString(event.button).c_str(),
                buttonStateToString(event.state).c_str(),
                event.timestamp,
                event.duration);
}

void ButtonManager::setDebounceDelay(unsigned long delay) {
    // This would update the debounce delay - for now it's a constant
    DEBUG_PRINTF("[Buttons] Debounce delay set to %lu ms\n", delay);
}

void ButtonManager::setLongPressTime(unsigned long time) {
    // This would update the long press time - for now it's a constant
    DEBUG_PRINTF("[Buttons] Long press time set to %lu ms\n", time);
}

void ButtonManager::testButtons() {
    DEBUG_PRINTLN("[Buttons] Testing buttons - press each button to test...");
    
    unsigned long testStart = millis();
    while (millis() - testStart < 10000) {  // 10 second test
        update();
        
        if (hasEvent()) {
            ButtonEvent event = getEvent();
            printEvent(event);
        }
        
        delay(10);
    }
    
    DEBUG_PRINTLN("[Buttons] Button test completed");
}

void ButtonManager::calibrateButtons() {
    DEBUG_PRINTLN("[Buttons] Button calibration - checking initial states...");
    
    DEBUG_PRINTF("[Buttons] UP pin %d: %s\n", upPin, digitalRead(upPin) ? "HIGH" : "LOW");
    DEBUG_PRINTF("[Buttons] DOWN pin %d: %s\n", downPin, digitalRead(downPin) ? "HIGH" : "LOW");
    DEBUG_PRINTF("[Buttons] OK pin %d: %s\n", okPin, digitalRead(okPin) ? "HIGH" : "LOW");
    
    DEBUG_PRINTLN("[Buttons] Calibration completed");
}
