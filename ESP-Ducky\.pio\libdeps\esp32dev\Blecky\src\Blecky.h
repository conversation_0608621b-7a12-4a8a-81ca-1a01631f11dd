/**
 * Blecky - DuckyScript Payload Executor for ESP32
 * 
 * A robust Arduino library that executes DuckyScript-style payloads
 * and sends them as BLE HID keyboard input to paired hosts.
 * 
 * Author: <PERSON> <<EMAIL>>
 * Organization: SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 * Version: 1.0.0
 * License: MIT
 * 
 * Features:
 * - Full DuckyScript command support (STRING, PRESS, DELAY, REPEAT, etc.)
 * - Dual backend support: Native ESP32 BLE + ESP32-BLE-Keyboard wrapper
 * - SPIFFS payload loading
 * - Robust ASCII to keycode mapping
 * - Windows compatibility optimizations
 * - Comprehensive debug logging
 */

#ifndef BLECKY_H
#define BLECKY_H

#include <Arduino.h>
#include <SPIFFS.h>

// Force use of ESP32-BLE-Keyboard backend for Windows compatibility
#define BLECKY_USE_TVK 1

// Forward declarations
class BleckyKeyboardBackend;

// Helper functions
void pressKey(String key);
void pressKeyPreserveCase(String key);

/**
 * Main Blecky class for DuckyScript payload execution
 */
class Blecky {
public:
    /**
     * Constructor
     * @param deviceName BLE device name (default: "<PERSON>lecky")
     * @param manufacturer Device manufacturer string (default: "SKR Electronics Lab")
     * @param battery Battery level percentage (default: 100)
     */
    Blecky(const char* deviceName = "Blecky", 
           const char* manufacturer = "SKR Electronics Lab", 
           uint8_t battery = 100);
    
    /**
     * Destructor
     */
    ~Blecky();
    
    /**
     * Initialize the BLE keyboard
     * @param waitForConnection If true, blocks until a host connects
     */
    void begin(bool waitForConnection = false);
    
    /**
     * Check if a host is connected
     * @return true if connected, false otherwise
     */
    bool isConnected() const;
    
    /**
     * Enable or disable debug logging
     * @param on true to enable debug output, false to disable
     */
    void setDebug(bool on);
    
    /**
     * Load payload from a string
     * @param payload DuckyScript payload string
     * @return true if payload was loaded successfully
     */
    bool setPayloadFromString(const String &payload);
    
    /**
     * Load payload from SPIFFS file
     * @param path File path (e.g., "/payload1.txt")
     * @return true if file was read successfully
     */
    bool setPayloadFromSPIFFS(const char* path);
    
    /**
     * Load payload from SD card (placeholder for future implementation)
     * @param path File path
     * @return true if file was read successfully
     */
    bool setPayloadFromSD(const char* path);
    
    /**
     * Execute the currently loaded payload
     * @return true if execution completed successfully
     */
    bool run();
    
    /**
     * Execute a single DuckyScript command
     * @param rawCmd Single command line (e.g., "STRING Hello World")
     */
    void executeCommand(const String &rawCmd);
    
    /**
     * Set default delay between key events
     * @param ms Delay in milliseconds
     */
    void setDefaultDelay(uint32_t ms);

private:
    BleckyKeyboardBackend* _backend;
    String _payload;
    String _lastCommand;
    uint32_t _defaultDelay;
    bool _debug;
    String _deviceName;
    String _manufacturer;
    uint8_t _battery;
    
    // Internal methods
    void debugPrint(const String &message);
    void parseAndExecuteLine(const String &line);
    void handleKeyCombo(const String &combo);
    bool parseKeyCombo(const String &combo, uint8_t &modifier, uint8_t &keycode);
    bool parseSpecialKey(const String &key, uint8_t &keycode);
    uint8_t charToKeycode(char c, bool &needShift);
    void sendKeyPress(uint8_t modifier, uint8_t keycode);
    void typeString(const String &text);
};

/**
 * Abstract backend interface for keyboard functionality
 */
class BleckyKeyboardBackend {
public:
    virtual ~BleckyKeyboardBackend() {}
    virtual void begin(const String &deviceName, const String &manufacturer, uint8_t battery, bool waitForConnection) = 0;
    virtual bool isConnected() const = 0;
    virtual void sendKey(uint8_t modifier, uint8_t keycode) = 0;
    virtual void releaseAll() = 0;
    virtual void typeText(const String &text) = 0;
    virtual void sendEnter() = 0;
};

// HID keycodes and modifiers
#ifndef BLECKY_USE_TVK
#define KEY_CTRL    0x01
#define KEY_SHIFT   0x02
#define KEY_ALT     0x04
#define KEY_GUI     0x08
#endif

#ifndef BLECKY_USE_TVK
#define KEY_A       0x04
#define KEY_B       0x05
#define KEY_C       0x06
#define KEY_D       0x07
#define KEY_E       0x08
#define KEY_F       0x09
#define KEY_G       0x0A
#define KEY_H       0x0B
#define KEY_I       0x0C
#define KEY_J       0x0D
#define KEY_K       0x0E
#define KEY_L       0x0F
#define KEY_M       0x10
#define KEY_N       0x11
#define KEY_O       0x12
#define KEY_P       0x13
#define KEY_Q       0x14
#define KEY_R       0x15
#define KEY_S       0x16
#define KEY_T       0x17
#define KEY_U       0x18
#define KEY_V       0x19
#define KEY_W       0x1A
#define KEY_X       0x1B
#define KEY_Y       0x1C
#define KEY_Z       0x1D

#define KEY_1       0x1E
#define KEY_2       0x1F
#define KEY_3       0x20
#define KEY_4       0x21
#define KEY_5       0x22
#define KEY_6       0x23
#define KEY_7       0x24
#define KEY_8       0x25
#define KEY_9       0x26
#define KEY_0       0x27

#define KEY_ENTER   0x28
#define KEY_ESC     0x29
#define KEY_BACKSPACE 0x2A
#define KEY_TAB     0x2B
#define KEY_SPACE   0x2C
#define KEY_MINUS   0x2D
#define KEY_EQUAL   0x2E
#define KEY_LEFTBRACE 0x2F
#define KEY_RIGHTBRACE 0x30
#define KEY_BACKSLASH 0x31
#define KEY_SEMICOLON 0x33
#define KEY_APOSTROPHE 0x34
#define KEY_GRAVE   0x35
#define KEY_COMMA   0x36
#define KEY_DOT     0x37
#define KEY_SLASH   0x38

#define KEY_F1      0x3A
#define KEY_F2      0x3B
#define KEY_F3      0x3C
#define KEY_F4      0x3D
#define KEY_F5      0x3E
#define KEY_F6      0x3F
#define KEY_F7      0x40
#define KEY_F8      0x41
#define KEY_F9      0x42
#define KEY_F10     0x43
#define KEY_F11     0x44
#define KEY_F12     0x45

#define KEY_INSERT  0x49
#define KEY_HOME    0x4A
#define KEY_PAGEUP  0x4B
#define KEY_DELETE  0x4C
#define KEY_END     0x4D
#define KEY_PAGEDOWN 0x4E
#define KEY_RIGHT   0x4F
#define KEY_LEFT    0x50
#define KEY_DOWN    0x51
#define KEY_UP      0x52
#endif // !BLECKY_USE_TVK

#endif // BLECKY_H
