/**
 * ESP-Ducky Display Management Implementation
 * 
 * Author: <PERSON>han - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#include "display.h"

// Global instance
DisplayManager displayManager;

DisplayManager::DisplayManager() {
    display = nullptr;
    currentMenu = MENU_SPLASH;
    previousMenu = MENU_SPLASH;
    selectedIndex = 0;
    scrollOffset = 0;
    lastUpdate = 0;
    needsRedraw = true;
    bluetoothConnected = false;
    wifiAPActive = false;
}

DisplayManager::~DisplayManager() {
    if (display) {
        delete display;
    }
}

bool DisplayManager::begin() {
    DEBUG_PRINTLN("[Display] Initializing display...");

    // Initialize I2C with proper frequency for OLED displays
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN, I2C_FREQUENCY);  // Use config values
    delay(200); // Allow I2C to stabilize - increased for 0.96" displays

    // Scan for I2C devices with improved error handling
    DEBUG_PRINTLN("[Display] Scanning I2C bus...");
    int deviceCount = 0;
    uint8_t foundAddress = 0;

    for (byte address = 1; address < 127; address++) {
        Wire.beginTransmission(address);
        byte error = Wire.endTransmission();
        if (error == 0) {
            DEBUG_PRINTF("[Display] I2C device found at address 0x%02X\n", address);
            deviceCount++;
            // Prioritize common OLED addresses
            if (address == 0x3D || address == 0x3C) {
                foundAddress = address;
            }
        }
        delay(1); // Small delay between scans
    }
    DEBUG_PRINTF("[Display] Found %d I2C device(s)\n", deviceCount);

    if (deviceCount == 0) {
        DEBUG_PRINTLN("[Display] ERROR: No I2C devices found!");
        DEBUG_PRINTLN("[Display] Check connections: SDA=21, SCL=22, VCC=3.3V, GND=GND");
        DEBUG_PRINTLN("[Display] Verify pull-up resistors on SDA/SCL lines");
        return false;
    }

    // Create display object
    display = new Adafruit_SSD1306(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

    // Try different I2C addresses, prioritizing 0x3C for 0.96" displays
    uint8_t addresses[] = {SCREEN_ADDRESS, 0x3C, 0x3D};
    bool displayFound = false;

    for (int i = 0; i < 3; i++) {
        if (addresses[i] == 0) continue; // Skip invalid address

        DEBUG_PRINTF("[Display] Trying address 0x%02X...\n", addresses[i]);

        // Add retry mechanism for display initialization
        for (int retry = 0; retry < 5; retry++) {
            if (display->begin(SSD1306_SWITCHCAPVCC, addresses[i])) {
                DEBUG_PRINTF("[Display] Display initialized at address 0x%02X (attempt %d)\n", addresses[i], retry + 1);
                displayFound = true;
                break;
            }
            delay(200); // Increased wait before retry for 0.96" displays
        }

        if (displayFound) break;
    }

    if (!displayFound) {
        DEBUG_PRINTLN("[Display] ERROR: Failed to initialize display at any address");
        DEBUG_PRINTLN("[Display] Troubleshooting steps:");
        DEBUG_PRINTLN("[Display] 1. Check connections: SDA=21, SCL=22, VCC=3.3V, GND=GND");
        DEBUG_PRINTLN("[Display] 2. Verify display type (SSD1306 compatible)");
        DEBUG_PRINTLN("[Display] 3. Check for loose connections");
        DEBUG_PRINTLN("[Display] 4. Try different I2C address (0x3C or 0x3D)");
        return false;
    }

    // Configure display with improved settings
    display->clearDisplay();
    display->setTextColor(SSD1306_WHITE);
    display->setTextSize(1);
    display->setCursor(0, 0);

    // Set display rotation and contrast based on display type
    #if DISPLAY_TYPE_1_3_INCH
    display->setRotation(0); // Normal orientation for 1.3" displays
    #else
    // For 0.96" displays, optimize for maximum visibility
    display->setRotation(0);

    // Enhanced contrast and brightness settings for 0.96" displays
    display->ssd1306_command(SSD1306_SETCONTRAST);
    display->ssd1306_command(0xFF); // Maximum contrast

    display->ssd1306_command(SSD1306_DISPLAYON);

    // Set precharge period for brighter display
    display->ssd1306_command(SSD1306_SETPRECHARGE);
    display->ssd1306_command(0xF1); // Phase 1: 1 DCLK, Phase 2: 15 DCLK

    // Set VCOM deselect level for better contrast
    display->ssd1306_command(SSD1306_SETVCOMDETECT);
    display->ssd1306_command(0x40); // 0.77 * VCC

    // Set display clock divide ratio and oscillator frequency
    display->ssd1306_command(SSD1306_SETDISPLAYCLOCKDIV);
    display->ssd1306_command(0x80); // Default ratio/frequency

    // Enable charge pump for better brightness
    display->ssd1306_command(SSD1306_CHARGEPUMP);
    display->ssd1306_command(0x14); // Enable charge pump
    #endif

    // Test display by showing a test pattern
    display->fillRect(0, 0, SCREEN_WIDTH, 2, SSD1306_WHITE);
    display->fillRect(0, SCREEN_HEIGHT-2, SCREEN_WIDTH, 2, SSD1306_WHITE);
    display->fillRect(0, 0, 2, SCREEN_HEIGHT, SSD1306_WHITE);
    display->fillRect(SCREEN_WIDTH-2, 0, 2, SCREEN_HEIGHT, SSD1306_WHITE);
    display->display();
    delay(500); // Show test pattern briefly

    DEBUG_PRINTLN("[Display] Display initialized successfully");
    DEBUG_PRINTLN("[Display] Test pattern displayed - display is working");

    // Show splash screen
    setMenu(MENU_SPLASH);
    forceRedraw();

    return true;
}

void DisplayManager::navigateUp() {
    if (currentMenu == MENU_SPLASH) return;
    
    if (selectedIndex > 0) {
        selectedIndex--;
        calculateScrollOffset();
        needsRedraw = true;
    }
}

void DisplayManager::navigateDown() {
    if (currentMenu == MENU_SPLASH) return;
    
    if (selectedIndex < (int)currentMenuItems.size() - 1) {
        selectedIndex++;
        calculateScrollOffset();
        needsRedraw = true;
    }
}

void DisplayManager::selectItem() {
    if (currentMenu == MENU_SPLASH) {
        setMenu(MENU_MAIN);
        return;
    }
    
    if (selectedIndex >= 0 && selectedIndex < (int)currentMenuItems.size()) {
        MenuItem item = currentMenuItems[selectedIndex];
        
        if (item.isBack) {
            goBack();
            return;
        }
        
        // Handle menu navigation based on current menu
        switch (currentMenu) {
            case MENU_MAIN:
                if (selectedIndex == 0) setMenu(MENU_PAYLOADS);
                else if (selectedIndex == 1) setMenu(MENU_MANAGE_PAYLOADS);
                else if (selectedIndex == 2) setMenu(MENU_ABOUT);
                break;
                
            case MENU_MANAGE_PAYLOADS:
                if (selectedIndex == 0) setMenu(MENU_DELETE_PAYLOADS);
                else if (selectedIndex == 1) setMenu(MENU_ADD_PAYLOADS);
                break;
                
            case MENU_PAYLOADS:
                // Execute payload (handled by main application)
                break;
                
            case MENU_DELETE_PAYLOADS:
                // Confirm delete (handled by main application)
                break;
                
            default:
                break;
        }
    }
}

void DisplayManager::goBack() {
    switch (currentMenu) {
        case MENU_PAYLOADS:
        case MENU_MANAGE_PAYLOADS:
        case MENU_ABOUT:
            setMenu(MENU_MAIN);
            break;
            
        case MENU_DELETE_PAYLOADS:
        case MENU_ADD_PAYLOADS:
            setMenu(MENU_MANAGE_PAYLOADS);
            break;
            
        case MENU_AP_MODE:
            setMenu(MENU_ADD_PAYLOADS);
            break;
            
        default:
            setMenu(MENU_MAIN);
            break;
    }
}

void DisplayManager::setMenu(MenuState menu) {
    previousMenu = currentMenu;
    currentMenu = menu;
    selectedIndex = 0;
    scrollOffset = 0;
    needsRedraw = true;
    
    // Update menu items based on new menu
    switch (menu) {
        case MENU_MAIN:
            updateMainMenu();
            break;
        case MENU_MANAGE_PAYLOADS:
            updateManagePayloadsMenu();
            break;
        case MENU_ABOUT:
            updateAboutMenu();
            break;
        default:
            break;
    }
}

void DisplayManager::updateMainMenu() {
    currentMenuItems.clear();
    currentMenuItems.push_back({MENU_PAYLOADS_TEXT, false, -1});
    currentMenuItems.push_back({MENU_MANAGE_PAYLOADS_TEXT, false, -1});
    currentMenuItems.push_back({MENU_ABOUT_TEXT, false, -1});
}

void DisplayManager::updatePayloadsMenu(const std::vector<String>& payloads) {
    currentMenuItems.clear();
    
    for (int i = 0; i < payloads.size(); i++) {
        currentMenuItems.push_back({payloads[i], false, i});
    }
    
    currentMenuItems.push_back({MENU_BACK_TEXT, true, -1});
    needsRedraw = true;
}

void DisplayManager::updateManagePayloadsMenu() {
    currentMenuItems.clear();
    currentMenuItems.push_back({SUBMENU_DELETE_PAYLOADS_TEXT, false, -1});
    currentMenuItems.push_back({SUBMENU_ADD_PAYLOADS_TEXT, false, -1});
    currentMenuItems.push_back({MENU_BACK_TEXT, true, -1});
}

void DisplayManager::updateDeletePayloadsMenu(const std::vector<String>& payloads) {
    currentMenuItems.clear();
    
    for (int i = 0; i < payloads.size(); i++) {
        currentMenuItems.push_back({payloads[i], false, i});
    }
    
    currentMenuItems.push_back({MENU_BACK_TEXT, true, -1});
    needsRedraw = true;
}

void DisplayManager::updateAboutMenu() {
    currentMenuItems.clear();
    currentMenuItems.push_back({MENU_BACK_TEXT, true, -1});
}

void DisplayManager::setBluetoothStatus(bool connected) {
    if (bluetoothConnected != connected) {
        bluetoothConnected = connected;
        needsRedraw = true;
    }
}

void DisplayManager::setWiFiAPStatus(bool active) {
    if (wifiAPActive != active) {
        wifiAPActive = active;
        needsRedraw = true;
    }
}

void DisplayManager::update() {
    if (!display || !needsRedraw) return;
    
    display->clearDisplay();
    
    switch (currentMenu) {
        case MENU_SPLASH:
            drawSplashScreen();
            break;
        case MENU_ABOUT:
            drawAboutScreen();
            break;
        case MENU_AP_MODE:
            drawAPModeScreen();
            break;
        default:
            drawHeader();
            drawMenu();
            break;
    }
    
    display->display();
    needsRedraw = false;
    lastUpdate = millis();
}

void DisplayManager::drawHeader() {
    // Draw status bar
    display->drawLine(0, HEADER_HEIGHT - 1, SCREEN_WIDTH - 1, HEADER_HEIGHT - 1, SSD1306_WHITE);
    
    // Bluetooth status (left side)
    display->setCursor(2, 2);
    display->setTextSize(1);
    if (bluetoothConnected) {
        display->print(ICON_BLUETOOTH_ON);
    } else {
        display->print(ICON_BLUETOOTH_OFF);
    }
    
    // WiFi status (right side)
    display->setCursor(SCREEN_WIDTH - 10, 2);
    if (wifiAPActive) {
        display->print(ICON_WIFI_ON);
    } else {
        display->print(ICON_WIFI_OFF);
    }
}

void DisplayManager::drawMenu() {
    int y = CONTENT_START_Y;
    int visibleItems = (SCREEN_HEIGHT - CONTENT_START_Y) / (CHAR_HEIGHT + 2);
    
    for (int i = scrollOffset; i < currentMenuItems.size() && i < scrollOffset + visibleItems; i++) {
        // Highlight selected item
        if (i == selectedIndex) {
            display->fillRect(0, y - 1, SCREEN_WIDTH, CHAR_HEIGHT + 2, SSD1306_WHITE);
            display->setTextColor(SSD1306_BLACK);
        } else {
            display->setTextColor(SSD1306_WHITE);
        }
        
        display->setCursor(2, y);
        display->setTextSize(1);
        
        String text = currentMenuItems[i].text;
        if (currentMenuItems[i].isBack) {
            text = "< " + text;
        }
        
        display->print(truncateText(text, SCREEN_WIDTH - 4));
        
        y += CHAR_HEIGHT + 2;
    }
    
    // Draw scrollbar if needed
    if (currentMenuItems.size() > visibleItems) {
        drawScrollbar();
    }
}

void DisplayManager::drawSplashScreen() {
    display->setTextColor(SSD1306_WHITE);

    // For 0.96" displays, use smaller text sizes for better fit
    #if DISPLAY_TYPE_1_3_INCH
    // Main title
    display->setTextSize(2);
    centerText(DEVICE_NAME, 20, 2);

    // Subtitle
    display->setTextSize(1);
    String subtitle = "by " + String(ORGANIZATION);
    centerText(subtitle, 45, 1);
    #else
    // Optimized for 0.96" display
    display->setTextSize(1);
    centerText(DEVICE_NAME, 15, 1);

    // Subtitle with better spacing
    String subtitle = "by " + String(ORGANIZATION);
    centerText(subtitle, 30, 1);

    // Version info
    centerText("v" + String(FIRMWARE_VERSION), 45, 1);
    #endif
}

void DisplayManager::drawAboutScreen() {
    drawHeader();
    
    display->setTextColor(SSD1306_WHITE);
    display->setTextSize(1);
    
    int y = CONTENT_START_Y + 2;
    
    // Device name
    display->setTextSize(2);
    centerText(DEVICE_NAME, y, 2);
    y += 20;
    
    // Developer
    display->setTextSize(1);
    centerText("Dev - " + String(AUTHOR), y, 1);
    y += 12;
    
    // Website
    centerText(WEBSITE, y, 1);
    y += 12;
    
    // Warning
    display->setCursor(2, y);
    display->print("Use for Educational");
    y += 10;
    display->setCursor(2, y);
    display->print("Purpose on your own");
    y += 10;
    display->setCursor(2, y);
    display->print("devices");
}

void DisplayManager::drawAPModeScreen() {
    display->setTextColor(SSD1306_WHITE);
    display->setTextSize(1);
    
    int y = 5;
    
    centerText("AP Mode Active", y, 1);
    y += 15;
    
    display->setCursor(2, y);
    display->print("SSID: ");
    display->print(DEFAULT_AP_SSID);
    y += 10;
    
    display->setCursor(2, y);
    display->print("Pass: ");
    display->print(DEFAULT_AP_PASSWORD);
    y += 10;
    
    display->setCursor(2, y);
    display->print("IP: ");
    display->print(AP_IP_ADDRESS);
    y += 15;
    
    centerText("Connect & Open IP", y, 1);
}

void DisplayManager::calculateScrollOffset() {
    int visibleItems = (SCREEN_HEIGHT - CONTENT_START_Y) / (CHAR_HEIGHT + 2);
    
    if (selectedIndex < scrollOffset) {
        scrollOffset = selectedIndex;
    } else if (selectedIndex >= scrollOffset + visibleItems) {
        scrollOffset = selectedIndex - visibleItems + 1;
    }
}

String DisplayManager::truncateText(String text, int maxWidth) {
    int maxChars = maxWidth / CHAR_WIDTH;
    if (text.length() <= maxChars) {
        return text;
    }
    return text.substring(0, maxChars - 3) + "...";
}

void DisplayManager::centerText(String text, int y, int textSize) {
    display->setTextSize(textSize);
    int textWidth = text.length() * CHAR_WIDTH * textSize;
    int x = (SCREEN_WIDTH - textWidth) / 2;
    display->setCursor(x, y);
    display->print(text);
}

void DisplayManager::drawScrollbar() {
    int visibleItems = (SCREEN_HEIGHT - CONTENT_START_Y) / (CHAR_HEIGHT + 2);
    int totalItems = currentMenuItems.size();
    
    if (totalItems <= visibleItems) return;
    
    int scrollbarHeight = (SCREEN_HEIGHT - CONTENT_START_Y) * visibleItems / totalItems;
    int scrollbarY = CONTENT_START_Y + (SCREEN_HEIGHT - CONTENT_START_Y) * scrollOffset / totalItems;
    
    display->drawRect(SCREEN_WIDTH - 3, CONTENT_START_Y, 2, SCREEN_HEIGHT - CONTENT_START_Y, SSD1306_WHITE);
    display->fillRect(SCREEN_WIDTH - 2, scrollbarY, 1, scrollbarHeight, SSD1306_WHITE);
}

void DisplayManager::forceRedraw() {
    needsRedraw = true;
}

void DisplayManager::clear() {
    if (display) {
        display->clearDisplay();
        display->display();
    }
}

void DisplayManager::showMessage(String message, int duration) {
    if (!display) return;
    
    display->clearDisplay();
    display->setTextColor(SSD1306_WHITE);
    display->setTextSize(1);
    centerText(message, SCREEN_HEIGHT / 2 - 4, 1);
    display->display();
    
    if (duration > 0) {
        delay(duration);
        forceRedraw();
    }
}

void DisplayManager::showProgress(String message, int percentage) {
    if (!display) return;

    display->clearDisplay();
    display->setTextColor(SSD1306_WHITE);
    display->setTextSize(1);

    // Message
    centerText(message, 20, 1);

    // Progress bar
    int barWidth = SCREEN_WIDTH - 20;
    int barHeight = 8;
    int barX = 10;
    int barY = 35;

    display->drawRect(barX, barY, barWidth, barHeight, SSD1306_WHITE);

    int fillWidth = (barWidth - 2) * percentage / 100;
    if (fillWidth > 0) {
        display->fillRect(barX + 1, barY + 1, fillWidth, barHeight - 2, SSD1306_WHITE);
    }

    // Percentage text
    String percentText = String(percentage) + "%";
    centerText(percentText, 50, 1);

    display->display();
}

void DisplayManager::showConfirmDialog(String message, String option1, String option2) {
    if (!display) return;

    display->clearDisplay();
    display->setTextColor(SSD1306_WHITE);
    display->setTextSize(1);

    // Message
    centerText(message, 15, 1);

    // Options
    display->setCursor(20, 35);
    display->print(option1);

    display->setCursor(80, 35);
    display->print(option2);

    // Selection indicator (simplified)
    display->setCursor(10, 35);
    display->print(">");

    display->display();
}
