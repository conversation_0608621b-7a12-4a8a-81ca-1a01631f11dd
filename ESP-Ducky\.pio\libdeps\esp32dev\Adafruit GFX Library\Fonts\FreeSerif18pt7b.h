#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerif18pt7bBitmaps[] PROGMEM = {
    0x6F, 0xFF, 0xFF, 0xFE, 0x66, 0x66, 0x66, 0x64, 0x40, 0x00, 0x6F, 0xF6,
    0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0x46, 0x42, 0x42, 0x42, 0x03, 0x06, 0x01,
    0x83, 0x00, 0xC1, 0x80, 0x61, 0xC0, 0x30, 0xC0, 0x38, 0x60, 0x18, 0x30,
    0xFF, 0xFF, 0x7F, 0xFF, 0x83, 0x06, 0x01, 0x86, 0x00, 0xC3, 0x00, 0xC1,
    0x87, 0xFF, 0xFF, 0xFF, 0xFE, 0x18, 0x30, 0x0C, 0x18, 0x06, 0x18, 0x06,
    0x0C, 0x03, 0x06, 0x01, 0x83, 0x00, 0xC1, 0x80, 0x60, 0xC0, 0x02, 0x00,
    0x10, 0x03, 0xE0, 0x64, 0xE6, 0x23, 0x61, 0x1B, 0x08, 0x58, 0x42, 0xE2,
    0x03, 0x90, 0x1F, 0x80, 0x7E, 0x00, 0xFC, 0x01, 0xF0, 0x0F, 0xC0, 0x4E,
    0x02, 0x38, 0x10, 0xE0, 0x87, 0x04, 0x3C, 0x21, 0xE1, 0x1B, 0xC9, 0xCF,
    0xFC, 0x1F, 0x80, 0x10, 0x00, 0x80, 0x07, 0x80, 0x20, 0x0F, 0xF0, 0x70,
    0x0F, 0x07, 0xD0, 0x0F, 0x02, 0x18, 0x07, 0x01, 0x18, 0x07, 0x00, 0x8C,
    0x03, 0x80, 0x4C, 0x01, 0x80, 0x44, 0x00, 0xC0, 0x26, 0x00, 0x60, 0x22,
    0x0F, 0x30, 0x33, 0x1F, 0xCC, 0x73, 0x1E, 0x37, 0xF1, 0x8E, 0x19, 0xE1,
    0x8E, 0x04, 0x00, 0x86, 0x02, 0x00, 0xC7, 0x01, 0x00, 0xC3, 0x80, 0x80,
    0x61, 0x80, 0x80, 0x60, 0xC0, 0x40, 0x30, 0x60, 0x40, 0x30, 0x38, 0xE0,
    0x30, 0x0F, 0xE0, 0x18, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x00, 0x7E, 0x00,
    0x00, 0x61, 0x80, 0x00, 0x60, 0x60, 0x00, 0x30, 0x30, 0x00, 0x18, 0x18,
    0x00, 0x0C, 0x0C, 0x00, 0x06, 0x0C, 0x00, 0x03, 0x8E, 0x00, 0x01, 0xCE,
    0x00, 0x00, 0x7C, 0x3F, 0xC0, 0x38, 0x07, 0x80, 0x3E, 0x03, 0x80, 0x77,
    0x01, 0x80, 0x73, 0xC0, 0x80, 0xF0, 0xF0, 0xC0, 0x70, 0x7C, 0xC0, 0x78,
    0x1E, 0x40, 0x3C, 0x07, 0xC0, 0x1E, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x0F,
    0xC0, 0xFF, 0x0D, 0xF0, 0xC7, 0xFC, 0x7F, 0xC1, 0xFC, 0x1F, 0x80, 0x3C,
    0x00, 0xFF, 0xFE, 0x92, 0x40, 0x00, 0x80, 0x80, 0x80, 0x80, 0x80, 0xC0,
    0xC0, 0x60, 0x70, 0x30, 0x18, 0x1C, 0x0E, 0x07, 0x03, 0x81, 0xC0, 0xE0,
    0x70, 0x38, 0x0C, 0x06, 0x03, 0x80, 0xC0, 0x60, 0x18, 0x0C, 0x03, 0x00,
    0xC0, 0x30, 0x0C, 0x80, 0x30, 0x0C, 0x03, 0x00, 0xC0, 0x60, 0x18, 0x0C,
    0x07, 0x01, 0x80, 0xC0, 0x70, 0x38, 0x1C, 0x0E, 0x07, 0x03, 0x81, 0xC0,
    0xE0, 0x60, 0x30, 0x38, 0x18, 0x0C, 0x0C, 0x04, 0x04, 0x04, 0x04, 0x04,
    0x00, 0x0C, 0x00, 0xC0, 0x0C, 0x0C, 0x46, 0xE4, 0xF7, 0x5E, 0x1F, 0x00,
    0xC0, 0x17, 0x8E, 0x4E, 0xE4, 0xFC, 0xC6, 0x0C, 0x00, 0xC0, 0x01, 0x80,
    0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80,
    0x01, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80,
    0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x6F, 0xFF,
    0x11, 0x24, 0x80, 0xFF, 0xFF, 0x6F, 0xF6, 0x00, 0xC0, 0x60, 0x18, 0x06,
    0x03, 0x80, 0xC0, 0x30, 0x1C, 0x06, 0x01, 0x80, 0xE0, 0x30, 0x0C, 0x07,
    0x01, 0x80, 0x60, 0x38, 0x0C, 0x03, 0x01, 0xC0, 0x60, 0x18, 0x0E, 0x03,
    0x00, 0x03, 0xE0, 0x0E, 0x70, 0x1C, 0x38, 0x38, 0x1C, 0x38, 0x1C, 0x78,
    0x1E, 0x70, 0x0E, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0,
    0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0x70, 0x0E, 0x70,
    0x0E, 0x78, 0x1E, 0x38, 0x1C, 0x38, 0x1C, 0x1C, 0x38, 0x0C, 0x30, 0x03,
    0xC0, 0x06, 0x03, 0x83, 0xE3, 0x38, 0x0E, 0x03, 0x80, 0xE0, 0x38, 0x0E,
    0x03, 0x80, 0xE0, 0x38, 0x0E, 0x03, 0x80, 0xE0, 0x38, 0x0E, 0x03, 0x80,
    0xE0, 0x38, 0x0E, 0x03, 0x81, 0xE1, 0xFF, 0x07, 0xC0, 0x1F, 0xF0, 0x3F,
    0xF8, 0x70, 0xF8, 0x60, 0x3C, 0xC0, 0x3C, 0x80, 0x1C, 0x00, 0x1C, 0x00,
    0x1C, 0x00, 0x18, 0x00, 0x18, 0x00, 0x30, 0x00, 0x30, 0x00, 0x60, 0x00,
    0xC0, 0x00, 0x80, 0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x01, 0x10,
    0x02, 0x3F, 0xFE, 0x7F, 0xFC, 0xFF, 0xFC, 0x0F, 0xC0, 0xFF, 0x0C, 0x3C,
    0x80, 0xE4, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x04, 0x00, 0x40, 0x04, 0x00,
    0xF8, 0x1F, 0xE0, 0x0F, 0x00, 0x1C, 0x00, 0xE0, 0x03, 0x00, 0x18, 0x00,
    0xC0, 0x06, 0x00, 0x60, 0x03, 0x78, 0x73, 0xFF, 0x0F, 0xC0, 0x00, 0x30,
    0x00, 0x30, 0x00, 0x70, 0x00, 0xF0, 0x00, 0xB0, 0x01, 0x30, 0x03, 0x30,
    0x06, 0x30, 0x04, 0x30, 0x08, 0x30, 0x18, 0x30, 0x10, 0x30, 0x20, 0x30,
    0x60, 0x30, 0xC0, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x30, 0x00, 0x30,
    0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x00, 0x7F, 0xC3,
    0xFE, 0x1F, 0xE1, 0x80, 0x08, 0x00, 0xC0, 0x07, 0xC0, 0x7F, 0x81, 0xFF,
    0x00, 0xFC, 0x01, 0xE0, 0x07, 0x80, 0x1C, 0x00, 0x60, 0x03, 0x00, 0x18,
    0x00, 0xC0, 0x06, 0x00, 0x60, 0x07, 0x78, 0x73, 0xFF, 0x0F, 0xC0, 0x00,
    0x0E, 0x00, 0xF8, 0x03, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1E, 0x00, 0x3C,
    0x00, 0x7C, 0x00, 0x79, 0xF0, 0x7F, 0xFC, 0xF8, 0x3C, 0xF0, 0x1E, 0xF0,
    0x1F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0x70, 0x0F, 0x78,
    0x0F, 0x78, 0x0E, 0x3C, 0x1E, 0x1E, 0x3C, 0x0F, 0xF8, 0x07, 0xE0, 0x3F,
    0xFD, 0xFF, 0xF7, 0xFF, 0xF0, 0x06, 0x80, 0x18, 0x00, 0x60, 0x03, 0x00,
    0x0C, 0x00, 0x30, 0x01, 0x80, 0x06, 0x00, 0x18, 0x00, 0xE0, 0x03, 0x00,
    0x0C, 0x00, 0x70, 0x01, 0x80, 0x06, 0x00, 0x38, 0x00, 0xC0, 0x03, 0x00,
    0x1C, 0x00, 0x60, 0x00, 0x0F, 0x83, 0xFC, 0x70, 0xE6, 0x07, 0xC0, 0x3C,
    0x03, 0xC0, 0x3E, 0x03, 0x70, 0x67, 0x8C, 0x3D, 0x81, 0xF0, 0x0F, 0x81,
    0x7C, 0x21, 0xE6, 0x0E, 0xC0, 0x7C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x36,
    0x06, 0x70, 0xE3, 0xFC, 0x0F, 0x80, 0x07, 0xC0, 0x1F, 0xF0, 0x3C, 0x78,
    0x38, 0x3C, 0x78, 0x1E, 0x70, 0x1E, 0xF0, 0x0E, 0xF0, 0x0F, 0xF0, 0x0F,
    0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF8, 0x0F, 0x78, 0x0F, 0x3C, 0x3F,
    0x1F, 0xEE, 0x0F, 0x9E, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x38, 0x00, 0x78,
    0x00, 0xF0, 0x01, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x70, 0x00, 0x6F, 0xF6,
    0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0x60, 0x67, 0xBC, 0xC0, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x19, 0xEF, 0x78, 0x42, 0x22, 0x20, 0x00, 0x00, 0xC0,
    0x00, 0xF0, 0x01, 0xF8, 0x01, 0xF8, 0x01, 0xF8, 0x01, 0xF0, 0x03, 0xF0,
    0x03, 0xF0, 0x00, 0xF0, 0x00, 0x3E, 0x00, 0x07, 0xE0, 0x00, 0x7E, 0x00,
    0x03, 0xE0, 0x00, 0x3E, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x03, 0xC0,
    0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x80,
    0x00, 0x3C, 0x00, 0x0F, 0xC0, 0x00, 0xFC, 0x00, 0x07, 0xC0, 0x00, 0x7C,
    0x00, 0x07, 0xE0, 0x00, 0x7E, 0x00, 0x07, 0xC0, 0x00, 0xF0, 0x00, 0xFC,
    0x00, 0xFC, 0x00, 0xF8, 0x01, 0xF8, 0x01, 0xF8, 0x01, 0xF8, 0x00, 0xF0,
    0x00, 0x30, 0x00, 0x00, 0x1F, 0x81, 0xFF, 0x18, 0x7D, 0x81, 0xEC, 0x07,
    0xF0, 0x3F, 0x81, 0xE0, 0x0F, 0x00, 0x70, 0x03, 0x80, 0x38, 0x01, 0x80,
    0x08, 0x00, 0xC0, 0x04, 0x00, 0x20, 0x02, 0x00, 0x10, 0x00, 0x80, 0x00,
    0x00, 0x00, 0x03, 0x00, 0x3C, 0x01, 0xE0, 0x07, 0x00, 0x00, 0x7F, 0x00,
    0x01, 0xFF, 0xC0, 0x07, 0x80, 0xF0, 0x0F, 0x00, 0x38, 0x1C, 0x00, 0x1C,
    0x38, 0x00, 0x0C, 0x38, 0x00, 0x06, 0x70, 0x1E, 0x02, 0x70, 0x3F, 0xE3,
    0xF0, 0x71, 0xE1, 0xE0, 0xE0, 0xC1, 0xE0, 0xC0, 0xC1, 0xE0, 0xC1, 0xC1,
    0xE1, 0x81, 0xC1, 0xE1, 0x81, 0x83, 0xE1, 0x83, 0x82, 0xE1, 0x83, 0x86,
    0x71, 0xC7, 0x8C, 0x70, 0xF9, 0xF8, 0x38, 0xF0, 0xF0, 0x3C, 0x00, 0x00,
    0x1E, 0x00, 0x00, 0x07, 0x80, 0x70, 0x03, 0xFF, 0xE0, 0x00, 0x7F, 0x00,
    0x00, 0x10, 0x00, 0x00, 0x38, 0x00, 0x00, 0x38, 0x00, 0x00, 0x38, 0x00,
    0x00, 0x7C, 0x00, 0x00, 0x5C, 0x00, 0x00, 0xDE, 0x00, 0x00, 0x8E, 0x00,
    0x01, 0x8F, 0x00, 0x01, 0x87, 0x00, 0x03, 0x07, 0x80, 0x03, 0x03, 0x80,
    0x02, 0x03, 0xC0, 0x06, 0x03, 0xC0, 0x07, 0xFF, 0xC0, 0x0F, 0xFF, 0xE0,
    0x0C, 0x01, 0xE0, 0x18, 0x00, 0xF0, 0x18, 0x00, 0xF0, 0x30, 0x00, 0x78,
    0x30, 0x00, 0x78, 0x70, 0x00, 0x7C, 0xFC, 0x01, 0xFF, 0xFF, 0xFC, 0x03,
    0xFF, 0xF8, 0x1E, 0x0F, 0xC1, 0xE0, 0x3C, 0x1E, 0x01, 0xE1, 0xE0, 0x1E,
    0x1E, 0x01, 0xE1, 0xE0, 0x1E, 0x1E, 0x03, 0xC1, 0xE0, 0x78, 0x1F, 0xFE,
    0x01, 0xFF, 0xF0, 0x1E, 0x07, 0xC1, 0xE0, 0x1E, 0x1E, 0x00, 0xF1, 0xE0,
    0x0F, 0x1E, 0x00, 0xF1, 0xE0, 0x0F, 0x1E, 0x00, 0xF1, 0xE0, 0x1E, 0x1E,
    0x07, 0xE3, 0xFF, 0xF8, 0xFF, 0xFE, 0x00, 0x00, 0xFE, 0x08, 0x0F, 0xFF,
    0x60, 0xFC, 0x1F, 0x87, 0xC0, 0x1E, 0x3C, 0x00, 0x38, 0xF0, 0x00, 0x67,
    0x80, 0x01, 0x9E, 0x00, 0x02, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00,
    0x3C, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00, 0x03, 0xC0, 0x00, 0x0F,
    0x00, 0x02, 0x1F, 0x00, 0x38, 0x3F, 0x03, 0x80, 0x7F, 0xFC, 0x00, 0x3F,
    0x80, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0x00, 0x78, 0x3F, 0x80, 0xF0, 0x0F,
    0x81, 0xE0, 0x0F, 0x83, 0xC0, 0x0F, 0x07, 0x80, 0x0F, 0x0F, 0x00, 0x1E,
    0x1E, 0x00, 0x1E, 0x3C, 0x00, 0x3C, 0x78, 0x00, 0x78, 0xF0, 0x00, 0xF1,
    0xE0, 0x01, 0xE3, 0xC0, 0x03, 0xC7, 0x80, 0x07, 0x8F, 0x00, 0x1E, 0x1E,
    0x00, 0x3C, 0x3C, 0x00, 0xF0, 0x78, 0x01, 0xE0, 0xF0, 0x0F, 0x81, 0xE0,
    0x7E, 0x07, 0xFF, 0xF0, 0x3F, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0x87, 0xFF,
    0xF8, 0x3C, 0x01, 0x83, 0xC0, 0x08, 0x3C, 0x00, 0x83, 0xC0, 0x00, 0x3C,
    0x00, 0x03, 0xC0, 0x00, 0x3C, 0x02, 0x03, 0xC0, 0x60, 0x3F, 0xFE, 0x03,
    0xFF, 0xE0, 0x3C, 0x06, 0x03, 0xC0, 0x20, 0x3C, 0x00, 0x03, 0xC0, 0x00,
    0x3C, 0x00, 0x03, 0xC0, 0x01, 0x3C, 0x00, 0x23, 0xC0, 0x06, 0x3C, 0x01,
    0xE7, 0xFF, 0xFE, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xBF, 0xFF, 0xCF, 0x00,
    0x67, 0x80, 0x13, 0xC0, 0x09, 0xE0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x3C,
    0x02, 0x1E, 0x03, 0x0F, 0xFF, 0x87, 0xFF, 0xC3, 0xC0, 0x61, 0xE0, 0x10,
    0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1E, 0x00, 0x0F, 0x00, 0x07, 0x80,
    0x03, 0xC0, 0x03, 0xF0, 0x03, 0xFC, 0x00, 0x00, 0xFE, 0x04, 0x07, 0xFF,
    0xB8, 0x1F, 0x03, 0xF0, 0xF8, 0x01, 0xE3, 0xE0, 0x01, 0xC7, 0x80, 0x01,
    0x9E, 0x00, 0x01, 0x3C, 0x00, 0x00, 0xF0, 0x00, 0x01, 0xE0, 0x00, 0x03,
    0xC0, 0x00, 0x07, 0x80, 0x07, 0xFF, 0x00, 0x07, 0xDE, 0x00, 0x07, 0xBC,
    0x00, 0x0F, 0x78, 0x00, 0x1E, 0x78, 0x00, 0x3C, 0xF0, 0x00, 0x78, 0xF0,
    0x00, 0xF1, 0xF0, 0x01, 0xE1, 0xF0, 0x03, 0xC1, 0xF8, 0x1F, 0x00, 0xFF,
    0xFC, 0x00, 0x3F, 0x80, 0xFF, 0x03, 0xFD, 0xF8, 0x07, 0xE3, 0xC0, 0x0F,
    0x0F, 0x00, 0x3C, 0x3C, 0x00, 0xF0, 0xF0, 0x03, 0xC3, 0xC0, 0x0F, 0x0F,
    0x00, 0x3C, 0x3C, 0x00, 0xF0, 0xF0, 0x03, 0xC3, 0xFF, 0xFF, 0x0F, 0xFF,
    0xFC, 0x3C, 0x00, 0xF0, 0xF0, 0x03, 0xC3, 0xC0, 0x0F, 0x0F, 0x00, 0x3C,
    0x3C, 0x00, 0xF0, 0xF0, 0x03, 0xC3, 0xC0, 0x0F, 0x0F, 0x00, 0x3C, 0x3C,
    0x00, 0xF1, 0xF8, 0x07, 0xEF, 0xF0, 0x3F, 0xC0, 0xFF, 0xBF, 0x0F, 0x07,
    0x83, 0xC1, 0xE0, 0xF0, 0x78, 0x3C, 0x1E, 0x0F, 0x07, 0x83, 0xC1, 0xE0,
    0xF0, 0x78, 0x3C, 0x1E, 0x0F, 0x07, 0x83, 0xC3, 0xF3, 0xFE, 0x0F, 0xF0,
    0x7E, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0,
    0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0,
    0x3C, 0x03, 0xC6, 0x38, 0xF3, 0x8F, 0xF0, 0x7C, 0x00, 0xFF, 0x07, 0xFC,
    0xFC, 0x03, 0xC0, 0xF0, 0x07, 0x01, 0xE0, 0x1C, 0x03, 0xC0, 0x60, 0x07,
    0x81, 0x80, 0x0F, 0x06, 0x00, 0x1E, 0x18, 0x00, 0x3C, 0x60, 0x00, 0x79,
    0x80, 0x00, 0xFF, 0x00, 0x01, 0xFF, 0x00, 0x03, 0xDF, 0x00, 0x07, 0x8F,
    0x00, 0x0F, 0x0F, 0x00, 0x1E, 0x0F, 0x00, 0x3C, 0x0F, 0x00, 0x78, 0x0F,
    0x00, 0xF0, 0x1F, 0x01, 0xE0, 0x1F, 0x03, 0xC0, 0x1F, 0x0F, 0xC0, 0x3F,
    0x3F, 0xC1, 0xFF, 0x80, 0xFF, 0x00, 0x0F, 0xC0, 0x00, 0xF0, 0x00, 0x1E,
    0x00, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x0F, 0x00, 0x01, 0xE0, 0x00, 0x3C,
    0x00, 0x07, 0x80, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x03, 0xC0, 0x00, 0x78,
    0x00, 0x0F, 0x00, 0x01, 0xE0, 0x00, 0x3C, 0x00, 0x07, 0x80, 0x04, 0xF0,
    0x01, 0x1E, 0x00, 0x63, 0xC0, 0x3C, 0xFF, 0xFF, 0xBF, 0xFF, 0xE0, 0xFC,
    0x00, 0x03, 0xF9, 0xF0, 0x00, 0x1F, 0x87, 0x80, 0x01, 0xF8, 0x3E, 0x00,
    0x0F, 0xC1, 0xF0, 0x00, 0x5E, 0x0B, 0xC0, 0x06, 0xF0, 0x5E, 0x00, 0x37,
    0x82, 0x78, 0x03, 0x3C, 0x13, 0xC0, 0x19, 0xE0, 0x8F, 0x01, 0x8F, 0x04,
    0x78, 0x0C, 0x78, 0x21, 0xE0, 0xC3, 0xC1, 0x0F, 0x06, 0x1E, 0x08, 0x3C,
    0x60, 0xF0, 0x41, 0xE3, 0x07, 0x82, 0x07, 0xB0, 0x3C, 0x10, 0x3D, 0x81,
    0xE0, 0x81, 0xF8, 0x0F, 0x04, 0x07, 0xC0, 0x78, 0x20, 0x3C, 0x03, 0xC1,
    0x00, 0xE0, 0x1E, 0x1C, 0x06, 0x01, 0xFB, 0xF8, 0x10, 0x1F, 0xE0, 0xFC,
    0x00, 0xFE, 0x78, 0x00, 0x70, 0x78, 0x00, 0x40, 0xF8, 0x00, 0x81, 0xF8,
    0x01, 0x02, 0xF8, 0x02, 0x04, 0xF8, 0x04, 0x08, 0xF0, 0x08, 0x11, 0xF0,
    0x10, 0x21, 0xF0, 0x20, 0x41, 0xF0, 0x40, 0x81, 0xF0, 0x81, 0x01, 0xF1,
    0x02, 0x01, 0xE2, 0x04, 0x03, 0xE4, 0x08, 0x03, 0xE8, 0x10, 0x03, 0xF0,
    0x20, 0x03, 0xE0, 0x40, 0x03, 0xC0, 0x80, 0x03, 0x81, 0x00, 0x07, 0x07,
    0x00, 0x06, 0x3F, 0x80, 0x04, 0x00, 0x00, 0xFE, 0x00, 0x07, 0xFF, 0x00,
    0x3E, 0x0F, 0x80, 0xF0, 0x07, 0x83, 0xC0, 0x07, 0x87, 0x80, 0x07, 0x1E,
    0x00, 0x0F, 0x3C, 0x00, 0x1E, 0xF0, 0x00, 0x1F, 0xE0, 0x00, 0x3F, 0xC0,
    0x00, 0x7F, 0x80, 0x00, 0xFF, 0x00, 0x01, 0xFE, 0x00, 0x03, 0xFC, 0x00,
    0x07, 0xF8, 0x00, 0x0F, 0x78, 0x00, 0x3C, 0xF0, 0x00, 0x78, 0xE0, 0x01,
    0xE1, 0xE0, 0x03, 0xC1, 0xE0, 0x0F, 0x01, 0xF0, 0x7C, 0x00, 0xFF, 0xE0,
    0x00, 0x7F, 0x00, 0xFF, 0xF8, 0x1F, 0xFF, 0x83, 0xC1, 0xF0, 0xF0, 0x1E,
    0x3C, 0x07, 0xCF, 0x00, 0xF3, 0xC0, 0x3C, 0xF0, 0x0F, 0x3C, 0x03, 0xCF,
    0x01, 0xF3, 0xC0, 0x78, 0xF0, 0x7C, 0x3F, 0xFE, 0x0F, 0xFE, 0x03, 0xC0,
    0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00,
    0x3C, 0x00, 0x1F, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x07, 0xFF,
    0x00, 0x3E, 0x0F, 0x80, 0xF0, 0x07, 0x83, 0xC0, 0x07, 0x87, 0x80, 0x0F,
    0x1E, 0x00, 0x0F, 0x3C, 0x00, 0x1E, 0xF0, 0x00, 0x1D, 0xE0, 0x00, 0x3F,
    0xC0, 0x00, 0x7F, 0x80, 0x00, 0xFF, 0x00, 0x01, 0xFE, 0x00, 0x03, 0xFC,
    0x00, 0x07, 0xF8, 0x00, 0x0F, 0x70, 0x00, 0x1C, 0xF0, 0x00, 0x79, 0xE0,
    0x00, 0xF1, 0xE0, 0x03, 0xC1, 0xC0, 0x07, 0x01, 0xC0, 0x1C, 0x01, 0xE0,
    0xF0, 0x00, 0x7F, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x7C,
    0x00, 0x00, 0x7E, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x0F, 0xC0, 0xFF, 0xF0,
    0x03, 0xFF, 0xF0, 0x0F, 0x07, 0xC0, 0x78, 0x1E, 0x03, 0xC0, 0x78, 0x1E,
    0x03, 0xC0, 0xF0, 0x1E, 0x07, 0x80, 0xF0, 0x3C, 0x07, 0x81, 0xE0, 0x78,
    0x0F, 0x0F, 0x80, 0x7F, 0xF8, 0x03, 0xFE, 0x00, 0x1E, 0x78, 0x00, 0xF1,
    0xE0, 0x07, 0x87, 0x80, 0x3C, 0x3C, 0x01, 0xE0, 0xF0, 0x0F, 0x03, 0xC0,
    0x78, 0x0F, 0x03, 0xC0, 0x7C, 0x3F, 0x01, 0xF3, 0xFC, 0x07, 0xE0, 0x07,
    0x84, 0x1F, 0xFC, 0x3C, 0x3E, 0x30, 0x0E, 0x70, 0x06, 0x70, 0x06, 0x70,
    0x02, 0x78, 0x00, 0x7C, 0x00, 0x3F, 0x00, 0x1F, 0xC0, 0x0F, 0xE0, 0x03,
    0xF8, 0x00, 0xFC, 0x00, 0x3E, 0x00, 0x1F, 0x80, 0x0F, 0x80, 0x0F, 0xC0,
    0x0F, 0xE0, 0x0F, 0x70, 0x1E, 0x78, 0x3C, 0x4F, 0xF8, 0x43, 0xF0, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0xF0, 0x7C, 0x0F, 0x03, 0x80, 0xF0, 0x10,
    0x0F, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00,
    0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0xF0,
    0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00, 0x0F,
    0x00, 0x00, 0xF0, 0x00, 0x1F, 0x80, 0x03, 0xFC, 0x00, 0xFF, 0x01, 0xFD,
    0xF8, 0x01, 0xC3, 0xC0, 0x02, 0x0F, 0x00, 0x08, 0x3C, 0x00, 0x20, 0xF0,
    0x00, 0x83, 0xC0, 0x02, 0x0F, 0x00, 0x08, 0x3C, 0x00, 0x20, 0xF0, 0x00,
    0x83, 0xC0, 0x02, 0x0F, 0x00, 0x08, 0x3C, 0x00, 0x20, 0xF0, 0x00, 0x83,
    0xC0, 0x02, 0x0F, 0x00, 0x08, 0x3C, 0x00, 0x20, 0xF0, 0x00, 0x81, 0xE0,
    0x04, 0x07, 0x80, 0x30, 0x0F, 0x81, 0x80, 0x1F, 0xFC, 0x00, 0x1F, 0xC0,
    0x00, 0xFF, 0xC0, 0x7F, 0x3E, 0x00, 0x1E, 0x1E, 0x00, 0x0C, 0x0E, 0x00,
    0x18, 0x0F, 0x00, 0x18, 0x07, 0x00, 0x10, 0x07, 0x80, 0x30, 0x07, 0x80,
    0x30, 0x03, 0xC0, 0x60, 0x03, 0xC0, 0x60, 0x01, 0xE0, 0x40, 0x01, 0xE0,
    0xC0, 0x00, 0xF0, 0xC0, 0x00, 0xF1, 0x80, 0x00, 0x71, 0x80, 0x00, 0x7B,
    0x00, 0x00, 0x3B, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1E,
    0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x08, 0x00, 0xFF, 0x9F,
    0xF0, 0x3F, 0x9F, 0x03, 0xE0, 0x07, 0x07, 0x80, 0xF0, 0x03, 0x03, 0xC0,
    0x78, 0x01, 0x80, 0xE0, 0x1E, 0x00, 0x80, 0x78, 0x0F, 0x00, 0xC0, 0x1C,
    0x03, 0x80, 0x60, 0x0F, 0x01, 0xE0, 0x20, 0x07, 0x81, 0xF0, 0x30, 0x01,
    0xC0, 0xBC, 0x18, 0x00, 0xF0, 0xDE, 0x08, 0x00, 0x78, 0x67, 0x0C, 0x00,
    0x1E, 0x23, 0xC4, 0x00, 0x0F, 0x31, 0xE6, 0x00, 0x03, 0x90, 0x7B, 0x00,
    0x01, 0xF8, 0x3D, 0x00, 0x00, 0xFC, 0x0F, 0x80, 0x00, 0x3C, 0x07, 0xC0,
    0x00, 0x1E, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0xE0, 0x00, 0x03, 0x00, 0x70,
    0x00, 0x01, 0x80, 0x10, 0x00, 0x00, 0x80, 0x08, 0x00, 0x7F, 0xE0, 0xFF,
    0x0F, 0xC0, 0x1E, 0x03, 0xE0, 0x0E, 0x00, 0xF0, 0x06, 0x00, 0x3C, 0x06,
    0x00, 0x0F, 0x06, 0x00, 0x07, 0x86, 0x00, 0x01, 0xE6, 0x00, 0x00, 0x7B,
    0x00, 0x00, 0x3F, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x03,
    0xF0, 0x00, 0x03, 0x78, 0x00, 0x01, 0x9E, 0x00, 0x01, 0x87, 0x80, 0x01,
    0x83, 0xE0, 0x01, 0x80, 0xF0, 0x01, 0x80, 0x3C, 0x01, 0x80, 0x1F, 0x01,
    0xC0, 0x07, 0xC1, 0xE0, 0x03, 0xF3, 0xFE, 0x0F, 0xFE, 0xFF, 0xC0, 0xFF,
    0x7E, 0x00, 0x1C, 0x1E, 0x00, 0x18, 0x1F, 0x00, 0x30, 0x0F, 0x00, 0x60,
    0x07, 0x80, 0x60, 0x03, 0xC0, 0xC0, 0x03, 0xE1, 0x80, 0x01, 0xE1, 0x80,
    0x00, 0xF3, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3C, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00,
    0x00, 0x7E, 0x00, 0x01, 0xFF, 0x80, 0x3F, 0xFF, 0xF1, 0xFF, 0xFF, 0x9C,
    0x00, 0x78, 0xC0, 0x07, 0x84, 0x00, 0x38, 0x00, 0x03, 0xC0, 0x00, 0x3C,
    0x00, 0x03, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xE0, 0x00, 0x1E, 0x00, 0x01,
    0xE0, 0x00, 0x0E, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00,
    0x07, 0x00, 0x00, 0x78, 0x00, 0x47, 0x80, 0x06, 0x78, 0x00, 0x33, 0x80,
    0x07, 0x3F, 0xFF, 0xFB, 0xFF, 0xFF, 0xC0, 0xFF, 0x83, 0x06, 0x0C, 0x18,
    0x30, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC1, 0x83, 0x06,
    0x0C, 0x18, 0x30, 0x60, 0xC1, 0x83, 0x07, 0xF0, 0xC0, 0x18, 0x06, 0x01,
    0x80, 0x70, 0x0C, 0x03, 0x00, 0xE0, 0x18, 0x06, 0x01, 0xC0, 0x30, 0x0C,
    0x03, 0x80, 0x60, 0x18, 0x07, 0x00, 0xC0, 0x30, 0x0E, 0x01, 0x80, 0x60,
    0x1C, 0x03, 0xFE, 0x0C, 0x18, 0x30, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x18,
    0x30, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC1, 0x83, 0x06,
    0x0C, 0x1F, 0xF0, 0x03, 0x80, 0x0F, 0x00, 0x1F, 0x00, 0x76, 0x00, 0xCE,
    0x03, 0x8C, 0x06, 0x1C, 0x1C, 0x18, 0x30, 0x30, 0xE0, 0x31, 0x80, 0x67,
    0x00, 0x6C, 0x00, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0xC0, 0xE0, 0x70,
    0x18, 0x0C, 0x03, 0x1F, 0x03, 0x8C, 0x38, 0x31, 0xC1, 0x8E, 0x0C, 0x00,
    0x60, 0x0F, 0x01, 0x98, 0x30, 0xC3, 0x86, 0x38, 0x31, 0xC1, 0x8E, 0x0C,
    0x78, 0xE5, 0xFB, 0xCF, 0x0C, 0x00, 0x00, 0x38, 0x00, 0xF8, 0x00, 0x38,
    0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x39,
    0xF0, 0x3B, 0xFC, 0x3C, 0x3E, 0x38, 0x0E, 0x38, 0x0F, 0x38, 0x07, 0x38,
    0x07, 0x38, 0x07, 0x38, 0x07, 0x38, 0x07, 0x38, 0x06, 0x38, 0x0E, 0x38,
    0x0C, 0x3C, 0x1C, 0x1F, 0xF0, 0x07, 0xE0, 0x07, 0xE0, 0x7F, 0xE3, 0x87,
    0xD8, 0x0F, 0x60, 0x1B, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x03, 0x00,
    0x0E, 0x00, 0x3C, 0x01, 0x78, 0x19, 0xFF, 0xC3, 0xFE, 0x03, 0xE0, 0x00,
    0x00, 0x00, 0x1C, 0x00, 0x7C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00,
    0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x07, 0x9C, 0x1F, 0xDC, 0x38, 0x7C, 0x70,
    0x3C, 0x70, 0x1C, 0x60, 0x1C, 0xE0, 0x1C, 0xE0, 0x1C, 0xE0, 0x1C, 0xE0,
    0x1C, 0xE0, 0x1C, 0xF0, 0x1C, 0x70, 0x1C, 0x7C, 0x3E, 0x3F, 0xDF, 0x0F,
    0x90, 0x0F, 0x81, 0xFF, 0x08, 0x3C, 0x80, 0xE7, 0xFF, 0x7F, 0xFF, 0x00,
    0x18, 0x00, 0xC0, 0x07, 0x00, 0x38, 0x03, 0xE0, 0x37, 0x83, 0x3F, 0xF0,
    0xFF, 0x03, 0xF0, 0x01, 0xF0, 0x3F, 0xC3, 0x8E, 0x18, 0x00, 0xC0, 0x0E,
    0x00, 0x70, 0x03, 0x80, 0x1C, 0x03, 0xFE, 0x1F, 0xF0, 0x38, 0x01, 0xC0,
    0x0E, 0x00, 0x70, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x01,
    0xC0, 0x0E, 0x00, 0x70, 0x07, 0xC0, 0xFF, 0x80, 0x0F, 0xC0, 0x1F, 0xFF,
    0x38, 0xFF, 0x70, 0x70, 0x70, 0x70, 0x70, 0x30, 0x70, 0x30, 0x70, 0x30,
    0x38, 0x20, 0x1C, 0x60, 0x0F, 0x80, 0x10, 0x00, 0x20, 0x00, 0x60, 0x00,
    0x7F, 0xE0, 0x3F, 0xFC, 0x1F, 0xFE, 0x20, 0x06, 0x40, 0x02, 0xC0, 0x02,
    0xC0, 0x04, 0xF0, 0x18, 0x7F, 0xF0, 0x1F, 0x80, 0x00, 0x00, 0x38, 0x00,
    0xF8, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00,
    0x38, 0x00, 0x38, 0xF0, 0x3B, 0xF8, 0x3E, 0x3C, 0x3C, 0x1C, 0x38, 0x1C,
    0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C,
    0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x7C, 0x3E, 0xFE, 0x7F, 0x18, 0x3C,
    0x3C, 0x18, 0x00, 0x00, 0x00, 0x00, 0x04, 0x3C, 0x7C, 0x1C, 0x1C, 0x1C,
    0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x3C, 0xFF, 0x03, 0x03,
    0xC1, 0xE0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0xC3, 0xE0, 0x70,
    0x38, 0x1C, 0x0E, 0x07, 0x03, 0x81, 0xC0, 0xE0, 0x70, 0x38, 0x1C, 0x0E,
    0x07, 0x03, 0x81, 0xC0, 0xE0, 0x70, 0x37, 0x3B, 0xF8, 0xF8, 0x00, 0x00,
    0x1C, 0x00, 0x3E, 0x00, 0x07, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xE0,
    0x00, 0x70, 0x00, 0x38, 0x00, 0x1C, 0x3F, 0x8E, 0x0F, 0x07, 0x06, 0x03,
    0x86, 0x01, 0xC4, 0x00, 0xE4, 0x00, 0x7E, 0x00, 0x3F, 0x80, 0x1D, 0xC0,
    0x0E, 0x70, 0x07, 0x1C, 0x03, 0x8F, 0x01, 0xC3, 0xC0, 0xE0, 0xF0, 0xF8,
    0x3C, 0xFE, 0x7F, 0x80, 0x00, 0x1C, 0x7C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
    0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
    0x1C, 0x1C, 0x1C, 0x3C, 0xFF, 0x38, 0xF0, 0x7C, 0x3E, 0xFE, 0x7F, 0x83,
    0xE3, 0xF0, 0xE0, 0xE0, 0x70, 0x1C, 0x38, 0x1C, 0x07, 0x0E, 0x07, 0x01,
    0xC3, 0x81, 0xC0, 0x70, 0xE0, 0x70, 0x1C, 0x38, 0x1C, 0x07, 0x0E, 0x07,
    0x01, 0xC3, 0x81, 0xC0, 0x70, 0xE0, 0x70, 0x1C, 0x38, 0x1C, 0x07, 0x0E,
    0x07, 0x01, 0xC3, 0x81, 0xE0, 0x73, 0xF9, 0xFC, 0x7F, 0x38, 0xF0, 0xFB,
    0xF8, 0x3E, 0x3C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38,
    0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38,
    0x1C, 0x78, 0x3C, 0xFE, 0x7F, 0x07, 0xE0, 0x1F, 0xF8, 0x3C, 0x7C, 0x78,
    0x3E, 0x70, 0x1E, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F, 0xF0,
    0x0F, 0xF8, 0x0F, 0x78, 0x0E, 0x7C, 0x1C, 0x3E, 0x3C, 0x0F, 0xF0, 0x07,
    0xC0, 0x18, 0xF0, 0xFB, 0xFC, 0x3E, 0x1E, 0x38, 0x0E, 0x38, 0x0F, 0x38,
    0x07, 0x38, 0x07, 0x38, 0x07, 0x38, 0x07, 0x38, 0x07, 0x38, 0x06, 0x38,
    0x0E, 0x38, 0x0C, 0x3E, 0x1C, 0x3B, 0xF8, 0x39, 0xE0, 0x38, 0x00, 0x38,
    0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x7C, 0x00, 0xFF,
    0x00, 0x07, 0xC4, 0x1F, 0xEC, 0x3C, 0x3C, 0x70, 0x1C, 0x70, 0x1C, 0x60,
    0x1C, 0xE0, 0x1C, 0xE0, 0x1C, 0xE0, 0x1C, 0xE0, 0x1C, 0xE0, 0x1C, 0xF0,
    0x1C, 0x70, 0x1C, 0x78, 0x3C, 0x3F, 0xDC, 0x1F, 0x1C, 0x00, 0x1C, 0x00,
    0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x3E, 0x00,
    0xFF, 0x19, 0xFF, 0x7C, 0xF3, 0x9C, 0x03, 0x80, 0x70, 0x0E, 0x01, 0xC0,
    0x38, 0x07, 0x00, 0xE0, 0x1C, 0x03, 0x80, 0x70, 0x1F, 0x07, 0xF0, 0x3E,
    0x58, 0x7C, 0x0F, 0x03, 0xC0, 0x7C, 0x07, 0x80, 0xF8, 0x1F, 0x81, 0xF8,
    0x1E, 0x03, 0xC0, 0xF0, 0x3E, 0x1A, 0x7C, 0x10, 0x30, 0x70, 0xFE, 0xFE,
    0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x79,
    0x7E, 0x3C, 0xF8, 0x7C, 0x38, 0x3C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C,
    0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C, 0x38, 0x1C,
    0x38, 0x1C, 0x38, 0x1C, 0x3C, 0x7C, 0x1F, 0xDF, 0x0F, 0x18, 0xFE, 0x1F,
    0x7C, 0x06, 0x38, 0x04, 0x1C, 0x04, 0x1C, 0x0C, 0x0E, 0x08, 0x0E, 0x18,
    0x07, 0x10, 0x07, 0x10, 0x07, 0x20, 0x03, 0xA0, 0x03, 0xE0, 0x01, 0xC0,
    0x01, 0xC0, 0x00, 0x80, 0x00, 0x80, 0xFC, 0x7F, 0x1F, 0x78, 0x3C, 0x06,
    0x38, 0x1C, 0x04, 0x38, 0x1C, 0x04, 0x1C, 0x1C, 0x0C, 0x1C, 0x0E, 0x08,
    0x1C, 0x1E, 0x18, 0x0E, 0x17, 0x10, 0x0E, 0x37, 0x10, 0x07, 0x23, 0x30,
    0x07, 0x63, 0xA0, 0x07, 0x43, 0xE0, 0x03, 0xC1, 0xC0, 0x03, 0x81, 0xC0,
    0x01, 0x80, 0x80, 0x01, 0x00, 0x80, 0x7F, 0x7E, 0x1E, 0x0C, 0x07, 0x8C,
    0x01, 0xC4, 0x00, 0x76, 0x00, 0x3E, 0x00, 0x0E, 0x00, 0x03, 0x80, 0x03,
    0xE0, 0x01, 0x70, 0x01, 0x1C, 0x01, 0x8F, 0x01, 0x83, 0x80, 0x80, 0xE0,
    0xC0, 0x79, 0xF0, 0xFF, 0xFE, 0x0F, 0x7C, 0x06, 0x38, 0x06, 0x1C, 0x04,
    0x1C, 0x0C, 0x0E, 0x0C, 0x0E, 0x08, 0x0F, 0x18, 0x07, 0x10, 0x07, 0x90,
    0x03, 0xB0, 0x03, 0xA0, 0x01, 0xE0, 0x01, 0xE0, 0x00, 0xC0, 0x00, 0xC0,
    0x00, 0x80, 0x00, 0x80, 0x01, 0x80, 0x01, 0x00, 0x03, 0x00, 0x7E, 0x00,
    0x7C, 0x00, 0x78, 0x00, 0x7F, 0xF9, 0xFF, 0xE6, 0x07, 0x10, 0x38, 0x00,
    0xE0, 0x07, 0x00, 0x38, 0x01, 0xE0, 0x07, 0x00, 0x38, 0x01, 0xE0, 0x07,
    0x01, 0x38, 0x0D, 0xC0, 0x3F, 0xFF, 0xBF, 0xFE, 0x07, 0x0E, 0x1C, 0x18,
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x30, 0x60, 0x60,
    0x10, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x1C,
    0x0E, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0x70, 0x38, 0x18,
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x08, 0x06, 0x06,
    0x08, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x38,
    0x70, 0xE0, 0x3E, 0x00, 0x7F, 0x87, 0xE3, 0xFE, 0x00, 0x7C};

const GFXglyph FreeSerif18pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 9, 0, 1},         // 0x20 ' '
    {0, 4, 24, 12, 5, -23},     // 0x21 '!'
    {12, 8, 9, 14, 3, -23},     // 0x22 '"'
    {21, 17, 23, 17, 0, -22},   // 0x23 '#'
    {70, 13, 27, 17, 2, -24},   // 0x24 '$'
    {114, 25, 23, 29, 2, -22},  // 0x25 '%'
    {186, 25, 25, 27, 1, -24},  // 0x26 '&'
    {265, 3, 9, 7, 2, -23},     // 0x27 '''
    {269, 9, 30, 12, 2, -23},   // 0x28 '('
    {303, 9, 30, 12, 1, -22},   // 0x29 ')'
    {337, 12, 14, 18, 3, -23},  // 0x2A '*'
    {358, 16, 18, 20, 2, -17},  // 0x2B '+'
    {394, 4, 9, 9, 2, -3},      // 0x2C ','
    {399, 8, 2, 12, 1, -8},     // 0x2D '-'
    {401, 4, 4, 9, 2, -3},      // 0x2E '.'
    {403, 10, 24, 10, 0, -23},  // 0x2F '/'
    {433, 16, 24, 18, 1, -23},  // 0x30 '0'
    {481, 10, 24, 18, 3, -23},  // 0x31 '1'
    {511, 16, 24, 17, 1, -23},  // 0x32 '2'
    {559, 13, 24, 17, 2, -23},  // 0x33 '3'
    {598, 16, 23, 18, 0, -22},  // 0x34 '4'
    {644, 13, 24, 17, 2, -23},  // 0x35 '5'
    {683, 16, 24, 18, 1, -23},  // 0x36 '6'
    {731, 14, 23, 18, 1, -22},  // 0x37 '7'
    {772, 12, 25, 18, 2, -24},  // 0x38 '8'
    {810, 16, 26, 17, 1, -24},  // 0x39 '9'
    {862, 4, 17, 9, 2, -16},    // 0x3A ':'
    {871, 5, 22, 9, 2, -16},    // 0x3B ';'
    {885, 18, 18, 20, 1, -17},  // 0x3C '<'
    {926, 18, 9, 20, 1, -12},   // 0x3D '='
    {947, 18, 18, 20, 1, -17},  // 0x3E '>'
    {988, 13, 25, 16, 2, -24},  // 0x3F '?'
    {1029, 24, 25, 30, 3, -24}, // 0x40 '@'
    {1104, 24, 23, 25, 1, -22}, // 0x41 'A'
    {1173, 20, 23, 22, 1, -22}, // 0x42 'B'
    {1231, 22, 24, 23, 1, -23}, // 0x43 'C'
    {1297, 23, 23, 25, 1, -22}, // 0x44 'D'
    {1364, 20, 23, 21, 2, -22}, // 0x45 'E'
    {1422, 17, 23, 20, 2, -22}, // 0x46 'F'
    {1471, 23, 24, 25, 1, -23}, // 0x47 'G'
    {1540, 22, 23, 25, 2, -22}, // 0x48 'H'
    {1604, 9, 23, 11, 2, -22},  // 0x49 'I'
    {1630, 12, 23, 13, 0, -22}, // 0x4A 'J'
    {1665, 23, 23, 25, 2, -22}, // 0x4B 'K'
    {1732, 19, 23, 21, 2, -22}, // 0x4C 'L'
    {1787, 29, 23, 31, 1, -22}, // 0x4D 'M'
    {1871, 23, 23, 25, 1, -22}, // 0x4E 'N'
    {1938, 23, 24, 25, 1, -23}, // 0x4F 'O'
    {2007, 18, 23, 20, 1, -22}, // 0x50 'P'
    {2059, 23, 30, 25, 1, -23}, // 0x51 'Q'
    {2146, 21, 23, 23, 2, -22}, // 0x52 'R'
    {2207, 16, 24, 19, 1, -23}, // 0x53 'S'
    {2255, 20, 23, 21, 1, -22}, // 0x54 'T'
    {2313, 22, 23, 25, 2, -22}, // 0x55 'U'
    {2377, 24, 23, 25, 0, -22}, // 0x56 'V'
    {2446, 33, 23, 33, 0, -22}, // 0x57 'W'
    {2541, 25, 23, 25, 0, -22}, // 0x58 'X'
    {2613, 24, 23, 25, 1, -22}, // 0x59 'Y'
    {2682, 21, 23, 21, 0, -22}, // 0x5A 'Z'
    {2743, 7, 28, 12, 3, -22},  // 0x5B '['
    {2768, 10, 24, 10, 0, -23}, // 0x5C '\'
    {2798, 7, 28, 12, 2, -22},  // 0x5D ']'
    {2823, 15, 13, 16, 1, -22}, // 0x5E '^'
    {2848, 18, 2, 17, 0, 3},    // 0x5F '_'
    {2853, 8, 6, 9, 1, -23},    // 0x60 '`'
    {2859, 13, 16, 15, 2, -15}, // 0x61 'a'
    {2885, 16, 25, 17, 1, -24}, // 0x62 'b'
    {2935, 14, 16, 16, 1, -15}, // 0x63 'c'
    {2963, 16, 25, 17, 1, -24}, // 0x64 'd'
    {3013, 13, 16, 16, 1, -15}, // 0x65 'e'
    {3039, 13, 25, 13, 0, -24}, // 0x66 'f'
    {3080, 16, 24, 16, 1, -15}, // 0x67 'g'
    {3128, 16, 25, 17, 1, -24}, // 0x68 'h'
    {3178, 8, 24, 10, 0, -23},  // 0x69 'i'
    {3202, 9, 32, 12, 0, -23},  // 0x6A 'j'
    {3238, 17, 25, 18, 1, -24}, // 0x6B 'k'
    {3292, 8, 25, 9, 0, -24},   // 0x6C 'l'
    {3317, 26, 16, 27, 1, -15}, // 0x6D 'm'
    {3369, 16, 16, 17, 1, -15}, // 0x6E 'n'
    {3401, 16, 16, 17, 1, -15}, // 0x6F 'o'
    {3433, 16, 24, 17, 1, -15}, // 0x70 'p'
    {3481, 16, 24, 17, 1, -15}, // 0x71 'q'
    {3529, 11, 16, 12, 1, -15}, // 0x72 'r'
    {3551, 10, 16, 13, 1, -15}, // 0x73 's'
    {3571, 8, 19, 10, 2, -18},  // 0x74 't'
    {3590, 16, 16, 17, 1, -15}, // 0x75 'u'
    {3622, 16, 16, 16, 0, -15}, // 0x76 'v'
    {3654, 24, 16, 24, 0, -15}, // 0x77 'w'
    {3702, 17, 16, 17, 0, -15}, // 0x78 'x'
    {3736, 16, 24, 16, 0, -15}, // 0x79 'y'
    {3784, 14, 16, 15, 0, -15}, // 0x7A 'z'
    {3812, 8, 30, 17, 3, -23},  // 0x7B '{'
    {3842, 2, 24, 7, 2, -23},   // 0x7C '|'
    {3848, 8, 30, 17, 6, -22},  // 0x7D '}'
    {3878, 16, 4, 17, 1, -10}}; // 0x7E '~'

const GFXfont FreeSerif18pt7b PROGMEM = {(uint8_t *)FreeSerif18pt7bBitmaps,
                                         (GFXglyph *)FreeSerif18pt7bGlyphs,
                                         0x20, 0x7E, 42};

// Approx. 4558 bytes
