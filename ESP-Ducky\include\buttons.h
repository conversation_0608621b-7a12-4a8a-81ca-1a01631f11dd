/**
 * ESP-<PERSON><PERSON>
 * 
 * Handles button input with debouncing and navigation
 * Author: <PERSON>han - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#ifndef BUTTONS_H
#define BUTTONS_H

#include <Arduino.h>
#include <vector>
#include "config.h"

// Button states
enum ButtonState {
    BUTTON_IDLE,
    BUTTON_PRESSED,
    BUTTON_RELEASED,
    BUTTON_LONG_PRESS
};

// Button types
enum ButtonType {
    BUTTON_UP,
    BUTTON_DOWN,
    BUTTON_OK
};

// Button event structure
struct ButtonEvent {
    ButtonType button;
    ButtonState state;
    unsigned long timestamp;
    unsigned long duration;
};

class ButtonManager {
private:
    // Button pins
    int upPin;
    int downPin;
    int okPin;
    
    // Button states
    bool upPressed;
    bool downPressed;
    bool okPressed;
    
    // Previous button states
    bool upPrevious;
    bool downPrevious;
    bool okPrevious;
    
    // Debounce timing
    unsigned long upLastDebounce;
    unsigned long downLastDebounce;
    unsigned long okLastDebounce;
    
    // Long press timing
    unsigned long upPressStart;
    unsigned long downPressStart;
    unsigned long okPressStart;
    
    // Long press flags
    bool upLongPressed;
    bool downLongPressed;
    bool okLongPressed;
    
    // Event queue
    std::vector<ButtonEvent> eventQueue;
    
    // Private methods
    bool readButton(int pin);
    void processButton(ButtonType button, int pin, bool& pressed, bool& previous, 
                      unsigned long& lastDebounce, unsigned long& pressStart, bool& longPressed);
    void addEvent(ButtonType button, ButtonState state, unsigned long duration = 0);
    
public:
    ButtonManager();
    
    // Initialization
    void begin();
    
    // Update and event handling
    void update();
    bool hasEvent();
    ButtonEvent getEvent();
    void clearEvents();
    
    // Direct button state queries
    bool isUpPressed();
    bool isDownPressed();
    bool isOKPressed();
    bool isAnyPressed();
    
    // Button combination detection
    bool isUpDownPressed();
    bool isOKLongPressed();
    
    // Utility
    String buttonTypeToString(ButtonType button);
    String buttonStateToString(ButtonState state);
    void printEvent(ButtonEvent event);
    
    // Configuration
    void setDebounceDelay(unsigned long delay);
    void setLongPressTime(unsigned long time);
    
    // Calibration and testing
    void testButtons();
    void calibrateButtons();
};

// Global button manager instance
extern ButtonManager buttonManager;

#endif // BUTTONS_H
