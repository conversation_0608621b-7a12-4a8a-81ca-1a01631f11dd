/**
 * ESP-Ducky Payload Management
 * 
 * Handles payload storage, loading, and execution
 * Author: <PERSON> Raihan - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#ifndef PAYLOAD_MANAGER_H
#define PAYLOAD_MANAGER_H

#include <Arduino.h>
#include <SPIFFS.h>
#include <vector>
#include "config.h"

// Payload information structure
struct PayloadInfo {
    String name;
    String filename;
    String description;
    size_t size;
    String dateCreated;
    bool isDefault;
};

// Payload execution result
struct PayloadResult {
    bool success;
    String message;
    int commandsExecuted;
    int errors;
    unsigned long executionTime;
};

class PayloadManager {
private:
    std::vector<PayloadInfo> payloads;
    bool initialized;
    
    // Private methods
    bool createDefaultPayloads();
    bool createPayloadFile(String filename, String content, String description = "");
    String generateFilename(String name);
    bool isValidPayloadName(String name);
    String sanitizeFilename(String filename);
    
public:
    PayloadManager();
    
    // Initialization
    bool begin();
    void scanPayloads();
    
    // Payload management
    std::vector<PayloadInfo> getPayloads();
    std::vector<String> getPayloadNames();
    PayloadInfo getPayloadInfo(int index);
    PayloadInfo getPayloadInfo(String name);
    int getPayloadCount();
    
    // Payload operations
    bool addPayload(String name, String content, String description = "");
    bool deletePayload(int index);
    bool deletePayload(String name);
    bool renamePayload(int index, String newName);
    bool updatePayload(int index, String content);
    
    // Payload content
    String loadPayload(int index);
    String loadPayload(String name);
    bool savePayload(String filename, String content);
    
    // Payload execution
    PayloadResult executePayload(int index);
    PayloadResult executePayload(String name);
    PayloadResult executePayloadContent(String content);
    
    // File operations
    bool exportPayload(int index, String exportPath);
    bool importPayload(String importPath, String name = "");
    
    // Validation
    bool validatePayload(String content);
    std::vector<String> getPayloadErrors(String content);
    
    // Statistics
    size_t getTotalPayloadSize();
    size_t getAvailableSpace();
    int getDefaultPayloadCount();
    int getCustomPayloadCount();
    
    // Utility
    String formatFileSize(size_t bytes);
    String getCurrentDateTime();
    bool isPayloadNameExists(String name);
    
    // Default payloads
    bool createHelloWorldPayload();
    bool createRickRollPayload();
    bool createCMDHackPayload();
    bool createWiFiPasswordsPayload();
    bool createAndroidTestPayload();
    bool createSystemInfoPayload();
    bool createDisableDefenderPayload();
    bool createFakeUpdatePayload();
    bool createChromePasswordsPayload();
    bool createPowerShellReversePayload();
    
    // Backup and restore
    bool backupPayloads(String backupPath);
    bool restorePayloads(String backupPath);
    
    // Cleanup
    void cleanup();
    bool deleteAllCustomPayloads();
    bool resetToDefaults();
};

// Global payload manager instance
extern PayloadManager payloadManager;

#endif // PAYLOAD_MANAGER_H
