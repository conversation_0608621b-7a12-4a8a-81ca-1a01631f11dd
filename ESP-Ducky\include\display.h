/**
 * ESP-Ducky Display Management
 * 
 * Handles OLED display operations, menus, and UI rendering
 * Author: <PERSON> Raihan - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#ifndef DISPLAY_H
#define DISPLAY_H

#include <Arduino.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <Wire.h>
#include <vector>
#include "config.h"

// Menu states
enum MenuState {
    MENU_SPLASH,
    MENU_MAIN,
    MENU_PAYLOADS,
    MENU_MANAGE_PAYLOADS,
    MENU_DELETE_PAYLOADS,
    MENU_ADD_PAYLOADS,
    MENU_ABOUT,
    MENU_PAYLOAD_CONFIRM,
    MENU_DELETE_CONFIRM,
    MENU_AP_MODE
};

// Menu item structure
struct MenuItem {
    String text;
    bool isBack;
    int payloadIndex;  // For payload items
};

class DisplayManager {
private:
    Adafruit_SSD1306* display;
    MenuState currentMenu;
    MenuState previousMenu;
    int selectedIndex;
    int scrollOffset;
    unsigned long lastUpdate;
    bool needsRedraw;
    
    // Menu items
    std::vector<MenuItem> currentMenuItems;
    
    // Status indicators
    bool bluetoothConnected;
    bool wifiAPActive;
    
    // Private methods
    void drawHeader();
    void drawMenu();
    void drawSplashScreen();
    void drawAboutScreen();
    void drawAPModeScreen();
    void drawScrollbar();
    void calculateScrollOffset();
    String truncateText(String text, int maxWidth);
    void centerText(String text, int y, int textSize = 1);
    
public:
    DisplayManager();
    ~DisplayManager();
    
    // Initialization
    bool begin();
    
    // Menu navigation
    void navigateUp();
    void navigateDown();
    void selectItem();
    void goBack();
    void setMenu(MenuState menu);
    MenuState getCurrentMenu() { return currentMenu; }
    int getSelectedIndex() { return selectedIndex; }
    
    // Menu management
    void updateMainMenu();
    void updatePayloadsMenu(const std::vector<String>& payloads);
    void updateManagePayloadsMenu();
    void updateDeletePayloadsMenu(const std::vector<String>& payloads);
    void updateAboutMenu();
    
    // Status updates
    void setBluetoothStatus(bool connected);
    void setWiFiAPStatus(bool active);
    void updateStatus();
    
    // Display control
    void update();
    void forceRedraw();
    void clear();
    void showMessage(String message, int duration = 2000);
    void showProgress(String message, int percentage);
    void showConfirmDialog(String message, String option1 = "Yes", String option2 = "No");
    
    // Screen saver
    void enableScreenSaver();
    void disableScreenSaver();
    bool isScreenSaverActive();
    
    // Utility
    void drawBitmap(int x, int y, const uint8_t* bitmap, int width, int height);
    void drawIcon(int x, int y, String iconType);
};

// Global display manager instance
extern DisplayManager displayManager;

#endif // DISPLAY_H
