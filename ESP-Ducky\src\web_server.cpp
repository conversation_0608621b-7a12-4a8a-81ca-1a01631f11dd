/**
 * ESP-Ducky Web Server Implementation
 * 
 * Author: <PERSON> - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#include "web_server.h"

// Global instance
WebServerManager webServerManager;

WebServerManager::WebServerManager() {
    server = nullptr;
    serverActive = false;
    uploadStatus = UPLOAD_IDLE;
    uploadSize = 0;
    apSSID = DEFAULT_AP_SSID;
    apPassword = DEFAULT_AP_PASSWORD;
    deviceName = DEFAULT_BLE_NAME;
}

WebServerManager::~WebServerManager() {
    if (server) {
        delete server;
    }
}

bool WebServerManager::begin() {
    DEBUG_PRINTLN("[WebServer] Initializing web server...");
    
    // Create web server instance
    server = new WebServer(WEB_SERVER_PORT);
    
    if (!server) {
        DEBUG_PRINTLN("[WebServer] ERROR: Failed to create web server");
        return false;
    }
    
    DEBUG_PRINTLN("[WebServer] Web server initialized successfully");
    return true;
}

void WebServerManager::end() {
    if (server && serverActive) {
        server->stop();
        serverActive = false;
    }
    
    if (WiFi.getMode() != WIFI_OFF) {
        WiFi.softAPdisconnect(true);
        WiFi.mode(WIFI_OFF);
    }
    
    DEBUG_PRINTLN("[WebServer] Web server stopped");
}

bool WebServerManager::startAP() {
    DEBUG_PRINTLN("[WebServer] Starting Access Point...");
    
    // Configure AP
    WiFi.mode(WIFI_AP);
    
    IPAddress local_IP;
    IPAddress gateway;
    IPAddress subnet;
    
    local_IP.fromString(AP_IP_ADDRESS);
    gateway.fromString(AP_GATEWAY);
    subnet.fromString(AP_SUBNET);
    
    if (!WiFi.softAPConfig(local_IP, gateway, subnet)) {
        DEBUG_PRINTLN("[WebServer] ERROR: Failed to configure AP");
        return false;
    }
    
    if (!WiFi.softAP(apSSID.c_str(), apPassword.c_str(), AP_CHANNEL, 0, AP_MAX_CONNECTIONS)) {
        DEBUG_PRINTLN("[WebServer] ERROR: Failed to start AP");
        return false;
    }
    
    // Setup web server routes
    setupRoutes();
    
    // Start web server
    server->begin();
    serverActive = true;
    
    DEBUG_PRINTLN("[WebServer] Access Point started successfully");
    DEBUG_PRINTLN("[WebServer] SSID: " + apSSID);
    DEBUG_PRINTLN("[WebServer] Password: " + apPassword);
    DEBUG_PRINTLN("[WebServer] IP: " + WiFi.softAPIP().toString());
    DEBUG_PRINTLN("[WebServer] Web server running on port " + String(WEB_SERVER_PORT));
    
    return true;
}

bool WebServerManager::stopAP() {
    DEBUG_PRINTLN("[WebServer] Stopping Access Point...");
    
    if (server && serverActive) {
        server->stop();
        serverActive = false;
    }
    
    WiFi.softAPdisconnect(true);
    WiFi.mode(WIFI_OFF);
    
    DEBUG_PRINTLN("[WebServer] Access Point stopped");
    return true;
}

void WebServerManager::setupRoutes() {
    if (!server) return;
    
    // Root page
    server->on("/", HTTP_GET, [this]() { handleRoot(); });
    
    // API endpoints
    server->on("/api/payloads", HTTP_GET, [this]() { apiGetPayloads(); });
    server->on("/api/settings", HTTP_GET, [this]() { apiGetSettings(); });
    server->on("/api/settings", HTTP_POST, [this]() { apiUpdateSettings(); });
    server->on("/api/status", HTTP_GET, [this]() { apiGetStatus(); });
    server->on("/api/reboot", HTTP_POST, [this]() { apiReboot(); });
    
    // File upload
    server->on("/upload", HTTP_POST, [this]() { handleUpload(); }, [this]() { handleFileUpload(); });
    
    // 404 handler
    server->onNotFound([this]() { handleNotFound(); });
    
    DEBUG_PRINTLN("[WebServer] Routes configured");
}

void WebServerManager::handleRoot() {
    DEBUG_PRINTLN("[WebServer] Serving root page");

    String html = "<!DOCTYPE html><html><head><title>ESP-Ducky</title>";
    html += "<style>body{font-family:Arial;background:#1a1a2e;color:#eee;margin:20px;}";
    html += ".card{background:#16213e;padding:20px;margin:10px 0;border-radius:8px;}";
    html += ".btn{background:#4fc3f7;color:#000;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;margin:5px;}";
    html += ".btn:hover{background:#29b6f6;}</style></head><body>";

    html += "<div class='card'><h1>ESP-Ducky</h1><p>Payload Management Interface</p></div>";

    html += "<div class='card'><h2>Upload New Payload</h2>";
    html += "<input type='file' id='fileInput' accept='.txt'>";
    html += "<button class='btn' onclick='uploadFile()'>Upload</button>";
    html += "<div id='uploadStatus'></div></div>";

    html += "<div class='card'><h2>Current Payloads</h2>";
    html += "<div id='payloadList'>Loading...</div></div>";

    html += "<div class='card'><h2>Device Settings</h2>";
    html += "<p>AP SSID: " + apSSID + "</p>";
    html += "<p>AP Password: " + apPassword + "</p>";
    html += "<p>Device Name: " + deviceName + "</p>";
    html += "<button class='btn' onclick='rebootDevice()'>Reboot Device</button></div>";

    html += "<div class='card'><p>made with love by SK Raihan (SKR Electronics Lab)</p>";
    html += "<p>Version: " + String(FIRMWARE_VERSION) + "</p></div>";

    html += "<script>";
    html += "function uploadFile(){alert('Upload feature coming soon!');}";
    html += "function loadPayloads(){document.getElementById('payloadList').innerHTML='<p>5 default payloads available</p>';}";
    html += "function rebootDevice(){if(confirm('Reboot device?')){fetch('/api/reboot',{method:'POST'}).then(()=>alert('Rebooting...'));}}";
    html += "loadPayloads();";
    html += "</script></body></html>";

    server->send(200, "text/html", html);
}

void WebServerManager::apiGetPayloads() {
    DEBUG_PRINTLN("[WebServer] API: Get payloads");
    
    DynamicJsonDocument doc(2048);
    JsonArray payloads = doc.createNestedArray("payloads");
    
    // This would get payloads from payload manager
    // For now, return empty array
    
    doc["success"] = true;
    doc["count"] = 0;
    
    String response;
    serializeJson(doc, response);
    server->send(200, "application/json", response);
}

void WebServerManager::apiGetSettings() {
    DEBUG_PRINTLN("[WebServer] API: Get settings");
    
    DynamicJsonDocument doc(1024);
    doc["apSSID"] = apSSID;
    doc["apPassword"] = apPassword;
    doc["deviceName"] = deviceName;
    doc["success"] = true;
    
    String response;
    serializeJson(doc, response);
    server->send(200, "application/json", response);
}

void WebServerManager::handleUpload() {
    server->send(200, "application/json", "{\"success\": false, \"message\": \"Upload not implemented yet\"}");
}

void WebServerManager::handleFileUpload() {
    // File upload handling would be implemented here
}

void WebServerManager::handleNotFound() {
    server->send(404, "text/plain", "Not Found");
}

void WebServerManager::apiUpdateSettings() {
    server->send(200, "application/json", "{\"success\": false, \"message\": \"Settings update not implemented yet\"}");
}

void WebServerManager::apiGetStatus() {
    DynamicJsonDocument doc(1024);
    doc["uptime"] = millis();
    doc["freeHeap"] = ESP.getFreeHeap();
    doc["connectedClients"] = WiFi.softAPgetStationNum();
    doc["success"] = true;
    
    String response;
    serializeJson(doc, response);
    server->send(200, "application/json", response);
}

void WebServerManager::apiReboot() {
    server->send(200, "application/json", "{\"success\": true, \"message\": \"Rebooting...\"}");
    delay(1000);
    ESP.restart();
}

bool WebServerManager::isAPActive() {
    return WiFi.getMode() == WIFI_AP && serverActive;
}

bool WebServerManager::isServerActive() {
    return serverActive;
}

String WebServerManager::getAPIP() {
    return WiFi.softAPIP().toString();
}

void WebServerManager::handleClient() {
    if (server && serverActive) {
        server->handleClient();
    }
}

void WebServerManager::update() {
    handleClient();
}

int WebServerManager::getConnectedClients() {
    return WiFi.softAPgetStationNum();
}
