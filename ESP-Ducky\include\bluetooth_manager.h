/**
 * ESP-<PERSON>y Bluetooth Management
 * 
 * Handles BLE HID keyboard functionality using <PERSON><PERSON><PERSON> library
 * Author: <PERSON> Raihan - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#ifndef BLUETOOTH_MANAGER_H
#define BLUETOOTH_MANAGER_H

#include <Arduino.h>
#include <Blecky.h>
#include "config.h"

// Connection states
enum BluetoothState {
    BT_DISABLED,
    BT_INITIALIZING,
    BT_ADVERTISING,
    BT_CONNECTED,
    BT_DISCONNECTED,
    BT_ERROR
};

// Execution status
enum ExecutionStatus {
    EXEC_IDLE,
    EXEC_RUNNING,
    EXEC_COMPLETED,
    EXEC_ERROR,
    EXEC_CANCELLED
};

class BluetoothManager {
private:
    Blecky* blecky;
    BluetoothState currentState;
    BluetoothState previousState;
    bool initialized;
    bool enabled;
    
    // Device configuration
    String deviceName;
    String manufacturer;
    
    // Connection tracking
    bool isConnected;
    unsigned long connectionTime;
    unsigned long lastActivity;
    int connectionCount;
    
    // Execution tracking
    ExecutionStatus executionStatus;
    String currentPayload;
    int executedCommands;
    int totalCommands;
    String lastError;
    
    // Statistics
    unsigned long totalExecutionTime;
    int successfulExecutions;
    int failedExecutions;

    // Connection stability tracking
    unsigned long lastConnectionCheck;
    unsigned long connectionStableTime;
    int reconnectAttempts;
    int maxReconnectAttempts;
    unsigned long connectionCheckInterval;

    // Private methods
    void updateConnectionState();
    void onConnectionChanged(bool connected);
    void resetExecutionState();
    void handleConnectionStability();
    
public:
    BluetoothManager();
    ~BluetoothManager();
    
    // Initialization
    bool begin();
    void end();
    
    // Device control
    bool enable();
    bool disable();
    bool isEnabled() { return enabled; }
    bool restart();
    
    // Configuration
    void setDeviceName(String name);
    void setManufacturer(String mfg);
    String getDeviceName() { return deviceName; }
    String getManufacturer() { return manufacturer; }
    
    // Connection management
    BluetoothState getState() { return currentState; }
    bool getConnectionStatus() { return isConnected; }
    unsigned long getConnectionTime() { return connectionTime; }
    unsigned long getLastActivity() { return lastActivity; }
    int getConnectionCount() { return connectionCount; }
    int getReconnectAttempts() { return reconnectAttempts; }
    
    // Payload execution
    bool executePayload(String payload);
    bool executePayloadFromFile(String filename);
    bool stopExecution();
    
    // Execution status
    ExecutionStatus getExecutionStatus() { return executionStatus; }
    String getCurrentPayload() { return currentPayload; }
    int getExecutedCommands() { return executedCommands; }
    int getTotalCommands() { return totalCommands; }
    float getExecutionProgress();
    String getLastError() { return lastError; }
    
    // Direct command execution
    bool sendCommand(String command);
    bool sendKeyPress(String keys);
    bool sendString(String text);
    bool sendDelay(int milliseconds);
    
    // Update and monitoring
    void update();
    void handleStateChange();
    
    // Statistics
    unsigned long getTotalExecutionTime() { return totalExecutionTime; }
    int getSuccessfulExecutions() { return successfulExecutions; }
    int getFailedExecutions() { return failedExecutions; }
    float getSuccessRate();
    void resetStatistics();
    
    // Utility
    String stateToString(BluetoothState state);
    String executionStatusToString(ExecutionStatus status);
    void printStatus();
    void printStatistics();
    
    // Testing
    bool testConnection();
    bool testKeyboard();
    bool runDiagnostics();
    
    // Power management
    void enterLowPowerMode();
    void exitLowPowerMode();
    bool isInLowPowerMode();
    
    // Security
    bool isPaired();
    void clearPairing();
    void setSecurityLevel(int level);
};

// Global bluetooth manager instance
extern BluetoothManager bluetoothManager;

#endif // BLUETOOTH_MANAGER_H
