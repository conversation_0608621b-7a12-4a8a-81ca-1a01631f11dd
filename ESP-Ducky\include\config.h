/**
 * ESP-Ducky Configuration File
 * 
 * Hardware and software configuration for ESP-Ducky device
 * Author: SK Raihan - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#ifndef CONFIG_H
#define CONFIG_H

// ============================================================================
// FIRMWARE VERSION
// ============================================================================
#define FIRMWARE_VERSION "1.0.0"
#define DEVICE_NAME "ESP-Ducky"
#define AUTHOR "SK Raihan"
#define ORGANIZATION "SKR Electronics Lab"
#define WEBSITE "www.skrelectronicslab.com"

// ============================================================================
// HARDWARE CONFIGURATION
// ============================================================================

// Display Configuration
#define DISPLAY_TYPE_1_3_INCH false    // Set to false for 0.96 inch display
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
#define SCREEN_ADDRESS 0x3D  // Try 0x3D first, fallback to 0x3C
#define I2C_FREQUENCY 400000  // 400kHz for stable OLED communication
#define I2C_SDA_PIN 21        // ESP32 default SDA pin
#define I2C_SCL_PIN 22        // ESP32 default SCL pin

// Display dimensions based on type
#if DISPLAY_TYPE_1_3_INCH
    #define DISPLAY_NAME "1.3 inch OLED"
    #define HEADER_HEIGHT 12
    #define CONTENT_START_Y 14
    #define LINES_PER_PAGE 4
    #define CHAR_WIDTH 6
    #define CHAR_HEIGHT 8
#else
    #define DISPLAY_NAME "0.96 inch OLED"
    #define HEADER_HEIGHT 10
    #define CONTENT_START_Y 12
    #define LINES_PER_PAGE 5
    #define CHAR_WIDTH 6
    #define CHAR_HEIGHT 8
#endif

// Button Configuration
#define BUTTON_UP_PIN 25
#define BUTTON_DOWN_PIN 26
#define BUTTON_OK_PIN 27
#define BUTTON_DEBOUNCE_DELAY 50
#define BUTTON_LONG_PRESS_TIME 1000

// LED Configuration (optional status LED)
#define STATUS_LED_PIN 2

// ============================================================================
// BLUETOOTH CONFIGURATION
// ============================================================================
#define DEFAULT_BLE_NAME "ESP-Ducky"
#define BLE_MANUFACTURER "SKR Electronics Lab"
#define BLE_BATTERY_LEVEL 100

// ============================================================================
// WIFI ACCESS POINT CONFIGURATION
// ============================================================================
#define DEFAULT_AP_SSID "ESP-Ducky"
#define DEFAULT_AP_PASSWORD "duckypass"
#define AP_IP_ADDRESS "***********"
#define AP_GATEWAY "***********"
#define AP_SUBNET "*************"
#define AP_CHANNEL 1
#define AP_MAX_CONNECTIONS 4

// ============================================================================
// WEB SERVER CONFIGURATION
// ============================================================================
#define WEB_SERVER_PORT 80
#define UPLOAD_MAX_SIZE 10240  // 10KB max payload size
#define MAX_PAYLOAD_NAME_LENGTH 32
#define MAX_PAYLOAD_COUNT 20

// ============================================================================
// SPIFFS CONFIGURATION
// ============================================================================
#define SPIFFS_FORMAT_ON_FAIL true
#define PAYLOADS_DIR "/payloads"
#define WEB_FILES_DIR "/web"
#define CONFIG_FILE "/config.json"

// ============================================================================
// UI CONFIGURATION
// ============================================================================
#define SPLASH_SCREEN_DURATION 2000  // 2 seconds
#define MENU_SCROLL_DELAY 200
#define STATUS_UPDATE_INTERVAL 1000
#define SCREEN_SAVER_TIMEOUT 300000  // 5 minutes

// Menu text
#define MENU_PAYLOADS_TEXT "Payloads"
#define MENU_MANAGE_PAYLOADS_TEXT "Mng Payloads"
#define MENU_ABOUT_TEXT "About"
#define SUBMENU_DELETE_PAYLOADS_TEXT "Delete Payloads"
#define SUBMENU_ADD_PAYLOADS_TEXT "Add Payloads"
#define MENU_BACK_TEXT "< Back"

// Status icons (using simple characters for now)
#define ICON_BLUETOOTH_ON "B"
#define ICON_BLUETOOTH_OFF "b"
#define ICON_WIFI_ON "W"
#define ICON_WIFI_OFF "w"

// ============================================================================
// DEFAULT PAYLOADS
// ============================================================================
#define DEFAULT_PAYLOAD_COUNT 10

// Payload file names (stored in SPIFFS)
#define PAYLOAD_HELLO_WORLD "hello_world.txt"
#define PAYLOAD_RICKROLL "rickroll.txt"
#define PAYLOAD_CMD_HACK "cmd_hack.txt"
#define PAYLOAD_WIFI_PASSWORDS "wifi_passwords.txt"
#define PAYLOAD_ANDROID_TEST "android_test.txt"
#define PAYLOAD_SYSTEM_INFO "system_info.txt"
#define PAYLOAD_DISABLE_DEFENDER "disable_defender.txt"
#define PAYLOAD_FAKE_UPDATE "fake_update.txt"
#define PAYLOAD_CHROME_PASSWORDS "chrome_passwords.txt"
#define PAYLOAD_POWERSHELL_REVERSE "powershell_reverse.txt"

// ============================================================================
// DEBUGGING
// ============================================================================
#define DEBUG_SERIAL true
#define DEBUG_BAUD_RATE 115200

#if DEBUG_SERIAL
    #define DEBUG_PRINT(x) Serial.print(x)
    #define DEBUG_PRINTLN(x) Serial.println(x)
    #define DEBUG_PRINTF(x, ...) Serial.printf(x, __VA_ARGS__)
#else
    #define DEBUG_PRINT(x)
    #define DEBUG_PRINTLN(x)
    #define DEBUG_PRINTF(x, ...)
#endif

// ============================================================================
// SYSTEM LIMITS
// ============================================================================
#define MAX_MENU_ITEMS 10
#define MAX_TEXT_LENGTH 128
#define WATCHDOG_TIMEOUT 30000  // 30 seconds

#endif // CONFIG_H
