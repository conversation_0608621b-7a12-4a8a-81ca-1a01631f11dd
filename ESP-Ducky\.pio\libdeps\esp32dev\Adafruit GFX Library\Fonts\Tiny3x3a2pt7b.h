#pragma once
#include <Adafruit_GFX.h>

/**
** The FontStruction “Tiny3x3a”
** (https://fontstruct.com/fontstructions/show/670512) by “Michaelangel007” is
** licensed under a Creative Commons Attribution Non-commercial Share Alike
*license
** (http://creativecommons.org/licenses/by-nc-sa/3.0/).
** “Tiny3x3a” was originally cloned (copied) from the FontStruction
** “CHECKER” (https://fontstruct.com/fontstructions/show/2391) by <PERSON> grant
** Grant, which is licensed under a Creative Commons Attribution Non-commercial
** Share Alike license (http://creativecommons.org/licenses/by-nc-sa/3.0/).
*
* Converted by eadmaster with fontconvert
**/

const uint8_t Tiny3x3a2pt7bBitmaps[] PROGMEM = {
    0xC0, 0xB4, 0xBF, 0x80, 0x6B, 0x00, 0xDD, 0x80, 0x59, 0x80, 0x80, 0x64,
    0x98, 0xF0, 0x5D, 0x00, 0xC0, 0xE0, 0x80, 0x2A, 0x00, 0x55, 0x00, 0x94,
    0xC9, 0x80, 0xEF, 0x80, 0xBC, 0x80, 0x6B, 0x00, 0x9F, 0x80, 0xE4, 0x80,
    0x7F, 0x00, 0xFC, 0x80, 0xA0, 0x58, 0x64, 0xE3, 0x80, 0x98, 0xD8, 0xD8,
    0x80, 0x5E, 0x80, 0xDF, 0x80, 0x71, 0x80, 0xD7, 0x00, 0xFB, 0x80, 0xFA,
    0x00, 0xD7, 0x80, 0xBE, 0x80, 0xE0, 0x27, 0x00, 0xBA, 0x80, 0x93, 0x80,
    0xFE, 0x80, 0xF6, 0x80, 0xF7, 0x80, 0xFE, 0x00, 0xF7, 0x00, 0xDE, 0x80,
    0x6B, 0x00, 0xE9, 0x00, 0xB7, 0x80, 0xB5, 0x00, 0xBF, 0x80, 0xAA, 0x80,
    0xA9, 0x00, 0xEB, 0x80, 0xEC, 0x88, 0x80, 0xDC, 0x54, 0xE0, 0x90, 0x70,
    0xBC, 0xF0, 0x7C, 0xB0, 0x68, 0xFC, 0xBC, 0xC0, 0x58, 0x9A, 0x80, 0xA4,
    0xDC, 0xD4, 0xF0, 0xF8, 0xF4, 0xE0, 0x60, 0x59, 0x80, 0xBC, 0xA8, 0xEC,
    0xF0, 0xAC, 0x80, 0x90, 0x79, 0x80, 0xF0, 0xCF, 0x00, 0x78};

const GFXglyph Tiny3x3a2pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 4, 0, 1},     // 0x20 ' '
    {0, 1, 2, 3, 1, -2},    // 0x21 '!'
    {1, 3, 2, 4, 0, -2},    // 0x22 '"'
    {2, 3, 3, 4, 0, -2},    // 0x23 '#'
    {4, 3, 3, 4, 0, -2},    // 0x24 '$'
    {6, 3, 3, 4, 0, -2},    // 0x25 '%'
    {8, 3, 3, 4, 0, -2},    // 0x26 '&'
    {10, 1, 1, 3, 1, -2},   // 0x27 '''
    {11, 2, 3, 3, 0, -2},   // 0x28 '('
    {12, 2, 3, 4, 1, -2},   // 0x29 ')'
    {13, 2, 2, 4, 1, -2},   // 0x2A '*'
    {14, 3, 3, 4, 0, -2},   // 0x2B '+'
    {16, 1, 2, 2, 0, 0},    // 0x2C ','
    {17, 3, 1, 4, 0, -1},   // 0x2D '-'
    {18, 1, 1, 2, 0, 0},    // 0x2E '.'
    {19, 3, 3, 4, 0, -2},   // 0x2F '/'
    {21, 3, 3, 4, 0, -2},   // 0x30 '0'
    {23, 2, 3, 3, 0, -2},   // 0x31 '1'
    {24, 3, 3, 4, 0, -2},   // 0x32 '2'
    {26, 3, 3, 4, 0, -2},   // 0x33 '3'
    {28, 3, 3, 4, 0, -2},   // 0x34 '4'
    {30, 3, 3, 4, 0, -2},   // 0x35 '5'
    {32, 3, 3, 4, 0, -2},   // 0x36 '6'
    {34, 3, 3, 4, 0, -2},   // 0x37 '7'
    {36, 3, 3, 4, 0, -2},   // 0x38 '8'
    {38, 3, 3, 4, 0, -2},   // 0x39 '9'
    {40, 1, 3, 3, 1, -2},   // 0x3A ':'
    {41, 2, 3, 3, 0, -1},   // 0x3B ';'
    {42, 2, 3, 3, 0, -2},   // 0x3C '<'
    {43, 3, 3, 4, 0, -2},   // 0x3D '='
    {45, 2, 3, 4, 1, -2},   // 0x3E '>'
    {46, 2, 3, 4, 1, -2},   // 0x3F '?'
    {47, 3, 3, 4, 0, -2},   // 0x40 '@'
    {49, 3, 3, 4, 0, -2},   // 0x41 'A'
    {51, 3, 3, 4, 0, -2},   // 0x42 'B'
    {53, 3, 3, 4, 0, -2},   // 0x43 'C'
    {55, 3, 3, 4, 0, -2},   // 0x44 'D'
    {57, 3, 3, 4, 0, -2},   // 0x45 'E'
    {59, 3, 3, 4, 0, -2},   // 0x46 'F'
    {61, 3, 3, 4, 0, -2},   // 0x47 'G'
    {63, 3, 3, 4, 0, -2},   // 0x48 'H'
    {65, 1, 3, 3, 1, -2},   // 0x49 'I'
    {66, 3, 3, 4, 0, -2},   // 0x4A 'J'
    {68, 3, 3, 4, 0, -2},   // 0x4B 'K'
    {70, 3, 3, 4, 0, -2},   // 0x4C 'L'
    {72, 3, 3, 4, 0, -2},   // 0x4D 'M'
    {74, 3, 3, 4, 0, -2},   // 0x4E 'N'
    {76, 3, 3, 4, 0, -2},   // 0x4F 'O'
    {78, 3, 3, 4, 0, -2},   // 0x50 'P'
    {80, 3, 3, 4, 0, -2},   // 0x51 'Q'
    {82, 3, 3, 4, 0, -2},   // 0x52 'R'
    {84, 3, 3, 4, 0, -2},   // 0x53 'S'
    {86, 3, 3, 4, 0, -2},   // 0x54 'T'
    {88, 3, 3, 4, 0, -2},   // 0x55 'U'
    {90, 3, 3, 4, 0, -2},   // 0x56 'V'
    {92, 3, 3, 4, 0, -2},   // 0x57 'W'
    {94, 3, 3, 4, 0, -2},   // 0x58 'X'
    {96, 3, 3, 4, 0, -2},   // 0x59 'Y'
    {98, 3, 3, 4, 0, -2},   // 0x5A 'Z'
    {100, 2, 3, 3, 0, -2},  // 0x5B '['
    {101, 3, 3, 4, 0, -2},  // 0x5C '\'
    {103, 2, 3, 4, 1, -2},  // 0x5D ']'
    {104, 3, 2, 4, 0, -2},  // 0x5E '^'
    {105, 3, 1, 4, 0, 0},   // 0x5F '_'
    {106, 2, 2, 3, 0, -2},  // 0x60 '`'
    {107, 2, 2, 3, 0, -1},  // 0x61 'a'
    {108, 2, 3, 3, 0, -2},  // 0x62 'b'
    {109, 2, 2, 3, 0, -1},  // 0x63 'c'
    {110, 2, 3, 3, 0, -2},  // 0x64 'd'
    {111, 2, 2, 3, 0, -1},  // 0x65 'e'
    {112, 2, 3, 3, 0, -2},  // 0x66 'f'
    {113, 2, 3, 3, 0, -1},  // 0x67 'g'
    {114, 2, 3, 3, 0, -2},  // 0x68 'h'
    {115, 1, 2, 2, 0, -1},  // 0x69 'i'
    {116, 2, 3, 3, 0, -1},  // 0x6A 'j'
    {117, 3, 3, 4, 0, -2},  // 0x6B 'k'
    {119, 2, 3, 3, 0, -2},  // 0x6C 'l'
    {120, 3, 2, 4, 0, -1},  // 0x6D 'm'
    {121, 3, 2, 4, 0, -1},  // 0x6E 'n'
    {122, 2, 2, 3, 0, -1},  // 0x6F 'o'
    {123, 2, 3, 3, 0, -1},  // 0x70 'p'
    {124, 2, 3, 3, 0, -1},  // 0x71 'q'
    {125, 2, 2, 3, 0, -1},  // 0x72 'r'
    {126, 2, 2, 3, 0, -1},  // 0x73 's'
    {127, 3, 3, 4, 0, -2},  // 0x74 't'
    {129, 3, 2, 4, 0, -1},  // 0x75 'u'
    {130, 3, 2, 4, 0, -1},  // 0x76 'v'
    {131, 3, 2, 4, 0, -1},  // 0x77 'w'
    {132, 2, 2, 3, 0, -1},  // 0x78 'x'
    {133, 3, 3, 4, 0, -1},  // 0x79 'y'
    {135, 2, 2, 3, 0, -1},  // 0x7A 'z'
    {136, 3, 3, 4, 0, -2},  // 0x7B '{'
    {138, 1, 4, 3, 1, -2},  // 0x7C '|'
    {139, 3, 3, 4, 0, -2},  // 0x7D '}'
    {141, 3, 2, 4, 0, -2}}; // 0x7E '~'

const GFXfont Tiny3x3a2pt7b PROGMEM = {(uint8_t *)Tiny3x3a2pt7bBitmaps,
                                       (GFXglyph *)Tiny3x3a2pt7bGlyphs, 0x20,
                                       0x7E, 4};

// Approx. 814 bytes
