[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Build flags
build_flags = 
    -DCORE_DEBUG_LEVEL=0
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Upload settings
upload_speed = 921600

; Library dependencies
lib_deps = 
    adafruit/Adafruit GFX Library@^1.11.9
    adafruit/Adafruit SSD1306@^2.5.7
    adafruit/Adafruit BusIO@^1.14.5
    bblanchon/Arduino<PERSON>son@^6.21.4
    https://github.com/T-vK/ESP32-BLE-Keyboard.git
    ../libraries/Blecky

; File system
board_build.filesystem = spiffs
board_build.partitions = huge_app.csv

; Extra scripts
extra_scripts = 
    pre:scripts/pre_build.py
    post:scripts/post_build.py

; Build type
build_type = release
