#!/usr/bin/env python3
"""
ESP-Ducky Post-Build Script
Generates combined firmware and manages release files

Author: <PERSON> - SKR Electronics Lab
Website: www.skrelectronicslab.com
"""

import os
import shutil
import json
import subprocess
from datetime import datetime

# Import PlatformIO environment
Import("env")

def get_version_info():
    """Get version information from version.json"""
    
    version_file = os.path.join(env.subst("$PROJECT_DIR"), "version.json")
    
    if os.path.exists(version_file):
        with open(version_file, 'r') as f:
            return json.load(f)
    
    return {"version_string": "1.0.0", "build_string": "*******"}

def create_combined_firmware():
    """Create combined firmware binary using esptool"""
    
    project_dir = env.subst("$PROJECT_DIR")
    build_dir = os.path.join(project_dir, ".pio", "build", "esp32dev")
    release_dir = os.path.join(project_dir, "Release")
    
    # Create release directory
    os.makedirs(release_dir, exist_ok=True)
    
    # Get version info
    version_data = get_version_info()
    version_string = version_data.get("version_string", "1.0.0")
    
    # Define file paths
    bootloader_bin = os.path.join(build_dir, "bootloader.bin")
    partitions_bin = os.path.join(build_dir, "partitions.bin")
    boot_app0_bin = os.path.join(build_dir, "boot_app0.bin")
    firmware_bin = os.path.join(build_dir, "firmware.bin")
    spiffs_bin = os.path.join(build_dir, "spiffs.bin")
    
    # Output firmware names
    combined_firmware = os.path.join(release_dir, f"ESP-Ducky-v{version_string}.bin")
    app_only_firmware = os.path.join(release_dir, f"ESP-Ducky-v{version_string}.bin")

    # Check if all required files exist
    required_files = [bootloader_bin, partitions_bin, boot_app0_bin, firmware_bin]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print(f"Warning: Missing files for combined firmware: {missing_files}")
        # Copy individual firmware file with clean name
        if os.path.exists(firmware_bin):
            shutil.copy2(firmware_bin, app_only_firmware)
            print(f"Created firmware: {app_only_firmware}")
        return
    
    try:
        # Create combined firmware using esptool
        esptool_cmd = [
            "python", "-m", "esptool",
            "--chip", "esp32",
            "merge_bin",
            "-o", combined_firmware,
            "--flash_mode", "dio",
            "--flash_freq", "40m",
            "--flash_size", "4MB",
            "0x1000", bootloader_bin,
            "0x8000", partitions_bin,
            "0xe000", boot_app0_bin,
            "0x10000", firmware_bin
        ]
        
        # Add SPIFFS if it exists
        if os.path.exists(spiffs_bin):
            esptool_cmd.extend(["0x290000", spiffs_bin])
        
        # Execute esptool command
        result = subprocess.run(esptool_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Combined firmware created: {combined_firmware}")
            
            # Create firmware info file
            info_file = os.path.join(release_dir, f"ESP-Ducky-v{version_string}-info.txt")
            with open(info_file, 'w') as f:
                f.write(f"ESP-Ducky Firmware v{version_string}\n")
                f.write(f"Build: {version_data.get('build_string', 'Unknown')}\n")
                f.write(f"Timestamp: {version_data.get('timestamp', 'Unknown')}\n")
                f.write(f"Author: SK Raihan - SKR Electronics Lab\n")
                f.write(f"Website: www.skrelectronicslab.com\n\n")
                f.write("Flash Instructions:\n")
                f.write("1. Connect ESP32 to computer\n")
                f.write("2. Put ESP32 in download mode (hold BOOT button while pressing RESET)\n")
                f.write("3. Use ESP32 Flash Download Tool or esptool:\n")
                f.write(f"   esptool.py --port COM_PORT write_flash 0x0 {os.path.basename(combined_firmware)}\n")
                f.write("4. Reset ESP32 after flashing\n")
            
            print(f"Firmware info created: {info_file}")
            
        else:
            print(f"Error creating combined firmware: {result.stderr}")
            # Fallback to copying individual firmware
            if os.path.exists(firmware_bin):
                shutil.copy2(firmware_bin, app_only_firmware)
                print(f"Created firmware: {app_only_firmware}")
    
    except Exception as e:
        print(f"Exception during firmware combination: {e}")
        # Fallback to copying individual firmware
        if os.path.exists(firmware_bin):
            shutil.copy2(firmware_bin, app_only_firmware)
            print(f"Created firmware: {app_only_firmware}")

def cleanup_old_releases():
    """Clean up old release files (keep last 5)"""
    
    project_dir = env.subst("$PROJECT_DIR")
    release_dir = os.path.join(project_dir, "Release")
    
    if not os.path.exists(release_dir):
        return
    
    # Get all firmware files
    firmware_files = []
    for file in os.listdir(release_dir):
        if file.startswith("ESP-Ducky-v") and file.endswith(".bin"):
            file_path = os.path.join(release_dir, file)
            firmware_files.append((file_path, os.path.getmtime(file_path)))
    
    # Sort by modification time (newest first)
    firmware_files.sort(key=lambda x: x[1], reverse=True)
    
    # Keep only the 5 most recent files
    if len(firmware_files) > 5:
        for file_path, _ in firmware_files[5:]:
            try:
                os.remove(file_path)
                # Also remove corresponding info file
                info_file = file_path.replace(".bin", "-info.txt")
                if os.path.exists(info_file):
                    os.remove(info_file)
                print(f"Removed old firmware: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"Error removing old firmware {file_path}: {e}")

def main():
    """Main post-build function"""
    
    print("=" * 50)
    print("ESP-Ducky Post-Build Script")
    print("SKR Electronics Lab")
    print("=" * 50)
    
    # Create combined firmware
    create_combined_firmware()
    
    # Cleanup old releases
    cleanup_old_releases()
    
    print("Post-build completed successfully")
    print("=" * 50)

# Execute main function
if __name__ == "__main__":
    main()
else:
    main()
