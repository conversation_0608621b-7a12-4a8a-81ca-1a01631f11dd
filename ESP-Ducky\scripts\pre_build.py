#!/usr/bin/env python3
"""
ESP-Ducky Pre-Build Script
Manages version numbering and build preparation

Author: <PERSON> - SKR Electronics Lab
Website: www.skrelectronicslab.com
"""

import os
import re
import json
from datetime import datetime

# Import PlatformIO environment
Import("env")

def update_version():
    """Update firmware version in all relevant files"""
    
    # Version file path
    version_file = os.path.join(env.subst("$PROJECT_DIR"), "version.json")
    config_file = os.path.join(env.subst("$PROJECT_DIR"), "include", "config.h")
    
    # Load or create version info
    if os.path.exists(version_file):
        with open(version_file, 'r') as f:
            version_data = json.load(f)
    else:
        version_data = {
            "major": 1,
            "minor": 0,
            "patch": 0,
            "build": 0
        }
    
    # Increment build number
    version_data["build"] += 1
    
    # Create version string
    version_string = f"{version_data['major']}.{version_data['minor']}.{version_data['patch']}"
    build_string = f"{version_string}.{version_data['build']}"
    
    # Add build timestamp
    version_data["timestamp"] = datetime.now().isoformat()
    version_data["version_string"] = version_string
    version_data["build_string"] = build_string
    
    # Save version file
    with open(version_file, 'w') as f:
        json.dump(version_data, f, indent=2)
    
    # Update config.h file
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config_content = f.read()
        
        # Update version in config.h
        config_content = re.sub(
            r'#define FIRMWARE_VERSION ".*"',
            f'#define FIRMWARE_VERSION "{version_string}"',
            config_content
        )
        
        with open(config_file, 'w') as f:
            f.write(config_content)
    
    print(f"ESP-Ducky Build {build_string}")
    print(f"Timestamp: {version_data['timestamp']}")
    
    return version_data

def prepare_spiffs():
    """Prepare SPIFFS data directory"""
    
    project_dir = env.subst("$PROJECT_DIR")
    data_dir = os.path.join(project_dir, "data")
    
    # Create data directory structure
    os.makedirs(os.path.join(data_dir, "payloads"), exist_ok=True)
    os.makedirs(os.path.join(data_dir, "web"), exist_ok=True)
    
    print("SPIFFS data directory prepared")

def main():
    """Main pre-build function"""
    
    print("=" * 50)
    print("ESP-Ducky Pre-Build Script")
    print("SKR Electronics Lab")
    print("=" * 50)
    
    # Update version
    version_data = update_version()
    
    # Prepare SPIFFS
    prepare_spiffs()
    
    print("Pre-build completed successfully")
    print("=" * 50)

# Execute main function
if __name__ == "__main__":
    main()
else:
    main()
