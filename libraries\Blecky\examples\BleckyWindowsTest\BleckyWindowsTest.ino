/**
 * Blecky Windows Compatibility Test
 * 
 * This example demonstrates the FIXED key combination handling
 * and tests various Windows shortcuts to ensure proper functionality.
 * 
 * Author: <PERSON> <<EMAIL>>
 * Organization: SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 * 
 * CRITICAL FIXES:
 * - GUI+r (lowercase 'r') opens Run dialog
 * - GUI+R (uppercase 'R') opens Game Bar (wrong!)
 * - Case sensitivity is now properly handled
 * 
 * Hardware Required:
 * - ESP32 development board
 * - Optional: LED on GPIO2, Button on GPIO0
 * 
 * Setup:
 * 1. Pair your ESP32 with your Windows computer via Bluetooth
 * 2. Upload this sketch
 * 3. Press the button or wait for auto-execution
 * 4. Watch as the correct applications open!
 */

#include <Blecky.h>

// Pin definitions
#define BUTTON_PIN 0   // Boot button on most ESP32 boards
#define LED_PIN 2

// Create Blecky instance
Blecky blecky("Blecky-WinTest", "SKR Electronics Lab", 100);

// Test state
int currentTest = 0;
bool testRunning = false;
unsigned long lastTestTime = 0;
const unsigned long TEST_INTERVAL = 10000; // 10 seconds between tests

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    // Initialize pins
    pinMode(BUTTON_PIN, INPUT_PULLUP);
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, LOW);
    
    Serial.println("===========================================");
    Serial.println("    Blecky Windows Compatibility Test");
    Serial.println("    SKR Electronics Lab");
    Serial.println("    Version 1.0.1 (FIXED)");
    Serial.println("===========================================");
    Serial.println();
    
    // Enable debug logging
    blecky.setDebug(true);
    
    // Set optimized delays for Windows
    blecky.setDefaultDelay(150);
    
    // Initialize BLE keyboard
    Serial.println("[SETUP] Initializing BLE keyboard...");
    blecky.begin(false); // Don't wait for connection during setup
    
    Serial.println("[SETUP] Setup complete!");
    Serial.println("[INFO] Press BOOT button (GPIO0) to run tests manually");
    Serial.println("[INFO] Or wait for automatic test execution");
    Serial.println();
    Serial.println("CRITICAL: This version fixes key combination case sensitivity!");
    Serial.println("- GUI+r (lowercase) = Opens Run dialog ✓");
    Serial.println("- GUI+R (uppercase) = Opens Game Bar ✗");
    Serial.println();
    
    // Blink LED to show we're ready
    for (int i = 0; i < 5; i++) {
        digitalWrite(LED_PIN, HIGH);
        delay(200);
        digitalWrite(LED_PIN, LOW);
        delay(200);
    }
}

void loop() {
    // Check for button press
    if (digitalRead(BUTTON_PIN) == LOW) {
        delay(50); // Debounce
        if (digitalRead(BUTTON_PIN) == LOW) {
            Serial.println("[BUTTON] Manual test trigger activated");
            runNextTest();
            delay(1000); // Prevent multiple triggers
        }
    }
    
    // Auto-run tests every 10 seconds
    if (!testRunning && (millis() - lastTestTime > TEST_INTERVAL)) {
        runNextTest();
    }
    
    // Blink LED to show system is alive
    static unsigned long lastBlink = 0;
    if (millis() - lastBlink > 1000) {
        digitalWrite(LED_PIN, !digitalRead(LED_PIN));
        lastBlink = millis();
    }
    
    delay(10);
}

void runNextTest() {
    if (testRunning) return;
    
    testRunning = true;
    lastTestTime = millis();
    
    Serial.println();
    Serial.println("===========================================");
    Serial.println("    RUNNING TEST " + String(currentTest + 1));
    Serial.println("===========================================");
    
    switch (currentTest) {
        case 0:
            testRunDialog();
            break;
        case 1:
            testNotepadBasic();
            break;
        case 2:
            testWindowsShortcuts();
            break;
        case 3:
            testTextEditing();
            break;
        case 4:
            testCaseSensitivity();
            break;
        default:
            currentTest = -1; // Will be incremented to 0
            break;
    }
    
    currentTest = (currentTest + 1) % 5;
    testRunning = false;
    
    Serial.println("[TEST] Test completed. Next test in 10 seconds...");
    Serial.println();
}

void testRunDialog() {
    Serial.println("[TEST 1] Testing Run Dialog (CRITICAL FIX)");
    Serial.println("[INFO] This should open the Run dialog, NOT Game Bar!");
    
    String payload = 
        "REM Test 1: Run Dialog (FIXED)\n"
        "DEFAULT_DELAY 150\n"
        "PRESS GUI+r\n"          // CRITICAL: lowercase 'r'
        "DELAY 600\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING TEST 1 PASSED: Run dialog opened correctly!\n"
        "ENTER\n"
        "STRING GUI+r (lowercase) works properly.\n";
    
    blecky.setPayloadFromString(payload);
    blecky.run();
}

void testNotepadBasic() {
    Serial.println("[TEST 2] Testing Basic Notepad Functionality");
    
    String payload = 
        "REM Test 2: Basic Notepad Test\n"
        "DEFAULT_DELAY 150\n"
        "PRESS GUI+r\n"          // lowercase 'r'
        "DELAY 500\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING === Blecky Windows Test 2 ===\n"
        "ENTER\n"
        "STRING Basic functionality test passed!\n"
        "ENTER\n"
        "STRING All key combinations working correctly.\n";
    
    blecky.setPayloadFromString(payload);
    blecky.run();
}

void testWindowsShortcuts() {
    Serial.println("[TEST 3] Testing Windows Shortcuts");
    
    String payload = 
        "REM Test 3: Windows Shortcuts\n"
        "DEFAULT_DELAY 200\n"
        "PRESS GUI+d\n"          // Show desktop
        "DELAY 1000\n"
        "PRESS GUI+e\n"          // Open File Explorer
        "DELAY 1500\n"
        "PRESS ALT+F4\n"         // Close File Explorer
        "DELAY 500\n"
        "PRESS GUI+r\n"          // Open Run dialog
        "DELAY 500\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING TEST 3: Windows shortcuts working!\n";
    
    blecky.setPayloadFromString(payload);
    blecky.run();
}

void testTextEditing() {
    Serial.println("[TEST 4] Testing Text Editing Shortcuts");
    
    String payload = 
        "REM Test 4: Text Editing\n"
        "DEFAULT_DELAY 150\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING This text will be copied and pasted.\n"
        "PRESS CTRL+a\n"         // Select all
        "DELAY 200\n"
        "PRESS CTRL+c\n"         // Copy
        "DELAY 200\n"
        "PRESS CTRL+v\n"         // Paste
        "DELAY 200\n"
        "ENTER\n"
        "STRING TEST 4: Copy/paste working!\n";
    
    blecky.setPayloadFromString(payload);
    blecky.run();
}

void testCaseSensitivity() {
    Serial.println("[TEST 5] Testing Case Sensitivity Fix");
    Serial.println("[INFO] This test demonstrates the critical fix");
    
    String payload = 
        "REM Test 5: Case Sensitivity Demonstration\n"
        "DEFAULT_DELAY 150\n"
        "PRESS GUI+r\n"          // lowercase 'r' - should open Run dialog
        "DELAY 600\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING === CASE SENSITIVITY TEST ===\n"
        "ENTER\n"
        "STRING CRITICAL FIX APPLIED:\n"
        "ENTER\n"
        "STRING - GUI+r (lowercase) = Run dialog ✓\n"
        "ENTER\n"
        "STRING - GUI+R (uppercase) = Game Bar ✗\n"
        "ENTER\n"
        "STRING This test used GUI+r (lowercase)\n"
        "ENTER\n"
        "STRING If you see this in Notepad, the fix works!\n";
    
    blecky.setPayloadFromString(payload);
    blecky.run();
}
