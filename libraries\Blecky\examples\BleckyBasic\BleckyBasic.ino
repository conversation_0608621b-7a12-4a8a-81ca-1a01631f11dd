/**
 * Blecky Basic Example
 * 
 * This example demonstrates basic usage of the Blecky library
 * to execute DuckyScript payloads via BLE HID keyboard.
 * 
 * Author: <PERSON> <<EMAIL>>
 * Organization: SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 * 
 * Hardware Required:
 * - ESP32 development board
 * - Optional: LED on GPIO2, Button on GPIO25
 * 
 * Setup:
 * 1. Pair your ESP32 with your computer via Bluetooth
 * 2. Upload this sketch
 * 3. Press the button or wait for auto-execution
 * 4. Watch as Notepad opens and text is typed!
 */

#include <Blecky.h>

// Pin definitions
#define BUTTON_PIN 25
#define LED_PIN 2

// Create Blecky instance
Blecky blecky("Blecky-Demo", "SKR Electronics Lab", 100);

// Button state tracking
bool lastButtonState = HIGH;
bool buttonPressed = false;
unsigned long lastDebounceTime = 0;
const unsigned long debounceDelay = 50;

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    // Initialize pins
    pinMode(BUTTON_PIN, INPUT_PULLUP);
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, LOW);
    
    Serial.println("=================================");
    Serial.println("    Blecky Basic Example");
    Serial.println("    SKR Electronics Lab");
    Serial.println("=================================");
    Serial.println();
    
    // Enable debug logging
    blecky.setDebug(true);
    
    // Set a reasonable default delay for reliability
    blecky.setDefaultDelay(100);
    
    // Initialize BLE keyboard
    Serial.println("[SETUP] Initializing BLE keyboard...");
    blecky.begin(false); // Don't wait for connection during setup
    
    Serial.println("[SETUP] Setup complete!");
    Serial.println("[INFO] Press button on GPIO25 to execute demo payload");
    Serial.println("[INFO] Or wait 10 seconds for auto-execution");
    Serial.println();
    
    // Blink LED to show we're ready
    for (int i = 0; i < 3; i++) {
        digitalWrite(LED_PIN, HIGH);
        delay(200);
        digitalWrite(LED_PIN, LOW);
        delay(200);
    }
}

void loop() {
    // Check button state with debouncing
    bool reading = digitalRead(BUTTON_PIN);
    
    if (reading != lastButtonState) {
        lastDebounceTime = millis();
    }
    
    if ((millis() - lastDebounceTime) > debounceDelay) {
        if (reading != buttonPressed) {
            buttonPressed = reading;
            
            // Button pressed (LOW because of INPUT_PULLUP)
            if (buttonPressed == LOW) {
                Serial.println("[BUTTON] Button pressed - executing demo payload!");
                executeDemoPayload();
            }
        }
    }
    
    lastButtonState = reading;
    
    // Auto-execute after 10 seconds if no button press
    static bool autoExecuted = false;
    if (!autoExecuted && millis() > 10000) {
        autoExecuted = true;
        Serial.println("[AUTO] Auto-executing demo payload after 10 seconds...");
        executeDemoPayload();
    }
    
    // Blink LED to show system is alive
    static unsigned long lastBlink = 0;
    if (millis() - lastBlink > 1000) {
        digitalWrite(LED_PIN, !digitalRead(LED_PIN));
        lastBlink = millis();
    }
    
    delay(10);
}

void executeDemoPayload() {
    Serial.println();
    Serial.println("=== EXECUTING DEMO PAYLOAD ===");
    
    // Check connection status
    if (blecky.isConnected()) {
        Serial.println("[STATUS] BLE connected - payload will execute on host");
    } else {
        Serial.println("[WARNING] BLE not connected - payload will execute but won't affect host");
        Serial.println("[INFO] Make sure your device is paired and connected");
    }
    
    // Define demo payload (FIXED - Case sensitive key combinations!)
    String demoPayload =
        "REM Blecky Demo Payload - FIXED VERSION\n"
        "REM Opens Notepad and types a demo message\n"
        "REM Author: SKR Electronics Lab\n"
        "REM CRITICAL: GUI+r uses lowercase 'r' to open Run dialog\n"
        "\n"
        "DEFAULT_DELAY 150\n"
        "\n"
        "REM Open Run dialog (CRITICAL: lowercase 'r'!)\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "\n"
        "REM Type notepad and press enter\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "\n"
        "REM Type demo message\n"
        "STRING === Blecky Demo - FIXED VERSION ===\n"
        "ENTER\n"
        "ENTER\n"
        "STRING Hello from Blecky!\n"
        "ENTER\n"
        "STRING This text was typed by an ESP32 via Bluetooth.\n"
        "ENTER\n"
        "ENTER\n"
        "STRING Library: Blecky v1.0.1 (FIXED)\n"
        "ENTER\n"
        "STRING Author: SK Raihan - SKR Electronics Lab\n"
        "ENTER\n"
        "STRING Website: www.skrelectronicslab.com\n"
        "ENTER\n"
        "ENTER\n"
        "STRING CRITICAL FIX: Key combinations are case-sensitive!\n"
        "ENTER\n"
        "STRING - GUI+r (lowercase) = Opens Run dialog\n"
        "ENTER\n"
        "STRING - GUI+R (uppercase) = Opens Game Bar/Recording\n"
        "ENTER\n"
        "ENTER\n"
        "STRING Features demonstrated:\n"
        "ENTER\n"
        "STRING - DuckyScript parsing\n"
        "ENTER\n"
        "STRING - BLE HID keyboard emulation\n"
        "ENTER\n"
        "STRING - FIXED key combinations (GUI+r)\n"
        "ENTER\n"
        "STRING - Text typing with special characters\n"
        "ENTER\n"
        "STRING - Configurable delays\n"
        "ENTER\n"
        "STRING - Windows compatibility optimizations\n"
        "ENTER\n"
        "ENTER\n"
        "STRING Demo completed successfully!\n"
        "ENTER\n"
        "\n"
        "REM Test some special characters\n"
        "STRING Special chars: !@#$%^&*()\n"
        "ENTER\n"
        "STRING Symbols: []{}|;:\"'<>?/\n"
        "ENTER\n"
        "\n"
        "REM Test REPEAT functionality\n"
        "STRING Repeated: \n"
        "STRING *\n"
        "REPEAT 10\n"
        "ENTER\n"
        "\n"
        "STRING === End of Demo ===\n";
    
    // Load and execute payload
    if (blecky.setPayloadFromString(demoPayload)) {
        Serial.println("[PAYLOAD] Demo payload loaded successfully");
        Serial.println("[EXECUTE] Starting execution...");
        
        bool success = blecky.run();
        
        if (success) {
            Serial.println("[SUCCESS] Demo payload executed successfully!");
        } else {
            Serial.println("[ERROR] Demo payload execution failed!");
        }
    } else {
        Serial.println("[ERROR] Failed to load demo payload!");
    }
    
    Serial.println("=== DEMO COMPLETE ===");
    Serial.println();
}
