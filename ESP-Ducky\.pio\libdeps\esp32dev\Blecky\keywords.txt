#######################################
# Blecky Library Keywords
# SKR Electronics Lab
#######################################

#######################################
# Datatypes (KEYWORD1)
#######################################

Blecky	KEYWORD1

#######################################
# Methods and Functions (KEYWORD2)
#######################################

begin	KEYWORD2
isConnected	KEYWORD2
setDebug	KEYWORD2
setPayloadFromString	KEYWORD2
setPayloadFromSPIFFS	KEYWORD2
setPayloadFromSD	KEYWORD2
run	KEYWORD2
executeCommand	KEYWORD2
setDefaultDelay	KEYWORD2

#######################################
# DuckyScript Commands (KEYWORD3)
#######################################

STRING	KEYWORD3
TYPE	KEYWORD3
PRESS	KEYWORD3
DELAY	KEYWORD3
DEFAULT_DELAY	KEYWORD3
REPEAT	KEYWORD3
ENTER	KEYWORD3
RETURN	KEYWORD3
TAB	KEYWORD3
ESC	KEYWORD3
ESCAPE	KEYWORD3
BACKSPACE	KEYWORD3
DELETE	KEYWORD3
DEL	KEYWORD3
INSERT	KEYWORD3
INS	KEYWORD3
HOME	KEYWORD3
END	KEYWORD3
PAGEUP	KEYWORD3
PGUP	KEYWORD3
PAGEDOWN	KEYWORD3
PGDN	KEYWORD3
UP	KEYWORD3
DOWN	KEYWORD3
LEFT	KEYWORD3
RIGHT	KEYWORD3
SPACE	KEYWORD3
CAPSLOCK	KEYWORD3
F1	KEYWORD3
F2	KEYWORD3
F3	KEYWORD3
F4	KEYWORD3
F5	KEYWORD3
F6	KEYWORD3
F7	KEYWORD3
F8	KEYWORD3
F9	KEYWORD3
F10	KEYWORD3
F11	KEYWORD3
F12	KEYWORD3

#######################################
# Modifiers (KEYWORD3)
#######################################

CTRL	KEYWORD3
CONTROL	KEYWORD3
SHIFT	KEYWORD3
ALT	KEYWORD3
GUI	KEYWORD3
WIN	KEYWORD3
WINDOWS	KEYWORD3
CMD	KEYWORD3

#######################################
# Comments (COMMENT)
#######################################

REM	COMMENT
