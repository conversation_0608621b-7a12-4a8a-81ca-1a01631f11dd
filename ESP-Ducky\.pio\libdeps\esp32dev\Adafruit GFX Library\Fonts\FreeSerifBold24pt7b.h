#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerifBold24pt7bBitmaps[] PROGMEM = {
    0x3C, 0x7E, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x7E, 0x7C, 0x7C,
    0x3C, 0x3C, 0x38, 0x38, 0x38, 0x38, 0x18, 0x10, 0x10, 0x10, 0x00, 0x00,
    0x00, 0x00, 0x3C, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x3C, 0x70, 0x07,
    0x7C, 0x07, 0xFE, 0x03, 0xFF, 0x01, 0xFF, 0x80, 0xFF, 0xC0, 0x7F, 0xC0,
    0x3E, 0xE0, 0x0E, 0x70, 0x07, 0x38, 0x03, 0x9C, 0x01, 0xC4, 0x00, 0xE2,
    0x00, 0x20, 0x00, 0xF0, 0x70, 0x01, 0xC0, 0xE0, 0x03, 0x81, 0xC0, 0x0F,
    0x07, 0x80, 0x1E, 0x0F, 0x00, 0x3C, 0x1E, 0x00, 0x78, 0x3C, 0x00, 0xF0,
    0x78, 0x01, 0xC0, 0xE0, 0x03, 0x81, 0xC0, 0xFF, 0xFF, 0xF9, 0xFF, 0xFF,
    0xF3, 0xFF, 0xFF, 0xE0, 0x78, 0x3C, 0x00, 0xF0, 0x78, 0x01, 0xC0, 0xE0,
    0x03, 0x81, 0xC0, 0x0F, 0x07, 0x80, 0x1E, 0x0F, 0x00, 0x3C, 0x1E, 0x0F,
    0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0x03, 0x81, 0xC0, 0x0F,
    0x07, 0x80, 0x1E, 0x0F, 0x00, 0x3C, 0x1E, 0x00, 0x78, 0x3C, 0x00, 0xF0,
    0x78, 0x01, 0xE0, 0xE0, 0x03, 0x81, 0xC0, 0x07, 0x07, 0x80, 0x1E, 0x0F,
    0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x00, 0x18, 0x00, 0x00, 0xC0, 0x00,
    0x7F, 0xF0, 0x0F, 0x37, 0xE0, 0xE1, 0x8F, 0x8E, 0x0C, 0x3C, 0x70, 0x60,
    0xE7, 0x83, 0x03, 0x3C, 0x18, 0x19, 0xF0, 0xC0, 0x4F, 0xC6, 0x02, 0x7F,
    0xF0, 0x03, 0xFF, 0x80, 0x0F, 0xFE, 0x00, 0x3F, 0xFC, 0x00, 0xFF, 0xF0,
    0x03, 0xFF, 0xE0, 0x0F, 0xFF, 0x80, 0x1F, 0xFE, 0x00, 0x3F, 0xF8, 0x01,
    0xFF, 0xC0, 0x0C, 0xFF, 0x00, 0x63, 0xFA, 0x03, 0x0F, 0xD0, 0x18, 0x3E,
    0x80, 0xC1, 0xF6, 0x06, 0x0F, 0xB8, 0x30, 0x79, 0xC1, 0x87, 0xCF, 0x0C,
    0x3C, 0x7E, 0x67, 0xC0, 0xFF, 0xF8, 0x00, 0xFE, 0x00, 0x00, 0xC0, 0x00,
    0x06, 0x00, 0x00, 0x30, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x30,
    0x00, 0x3E, 0x00, 0x0C, 0x00, 0x0F, 0xF0, 0x03, 0x80, 0x07, 0xE7, 0x03,
    0xE0, 0x01, 0xF8, 0x7F, 0xFC, 0x00, 0x3E, 0x07, 0xF7, 0x00, 0x0F, 0xC0,
    0x80, 0xE0, 0x03, 0xF0, 0x10, 0x38, 0x00, 0x7E, 0x02, 0x07, 0x00, 0x0F,
    0x80, 0x41, 0xC0, 0x03, 0xF0, 0x10, 0x30, 0x00, 0x7E, 0x02, 0x0E, 0x00,
    0x0F, 0x80, 0xC1, 0x80, 0x01, 0xF0, 0x10, 0x70, 0x00, 0x3E, 0x06, 0x1C,
    0x00, 0x07, 0xC1, 0x83, 0x80, 0x00, 0x7C, 0x60, 0xE0, 0x1F, 0x07, 0xF8,
    0x18, 0x0F, 0xF8, 0x7C, 0x07, 0x07, 0xF1, 0x00, 0x00, 0xC1, 0xF8, 0x10,
    0x00, 0x38, 0x3F, 0x02, 0x00, 0x06, 0x0F, 0xC0, 0x40, 0x01, 0xC3, 0xF0,
    0x08, 0x00, 0x30, 0x7E, 0x01, 0x00, 0x0E, 0x1F, 0x80, 0x40, 0x03, 0x83,
    0xF0, 0x08, 0x00, 0x60, 0x7E, 0x01, 0x00, 0x1C, 0x0F, 0x80, 0x40, 0x03,
    0x01, 0xF0, 0x18, 0x00, 0xE0, 0x3E, 0x02, 0x00, 0x18, 0x03, 0xC0, 0xC0,
    0x07, 0x00, 0x7C, 0x70, 0x00, 0xC0, 0x07, 0xFC, 0x00, 0x38, 0x00, 0x7E,
    0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x07, 0x8F,
    0xE0, 0x00, 0x03, 0xC1, 0xF8, 0x00, 0x00, 0xF0, 0x3F, 0x00, 0x00, 0x7C,
    0x07, 0xC0, 0x00, 0x1F, 0x01, 0xF0, 0x00, 0x07, 0xE0, 0x7C, 0x00, 0x01,
    0xF8, 0x1E, 0x00, 0x00, 0x7F, 0x07, 0x80, 0x00, 0x1F, 0xE3, 0x80, 0x00,
    0x03, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xE0, 0x3F,
    0xF0, 0x07, 0xFC, 0x01, 0xF0, 0x07, 0xFF, 0x00, 0x78, 0x07, 0xBF, 0xE0,
    0x1C, 0x03, 0x87, 0xFC, 0x07, 0x01, 0xE0, 0xFF, 0x81, 0x80, 0xF0, 0x3F,
    0xE0, 0xC0, 0x7C, 0x07, 0xFC, 0x30, 0x1F, 0x00, 0xFF, 0x98, 0x0F, 0xC0,
    0x3F, 0xFC, 0x03, 0xF0, 0x07, 0xFF, 0x00, 0xFE, 0x00, 0xFF, 0x80, 0x3F,
    0x80, 0x3F, 0xF0, 0x0F, 0xF0, 0x07, 0xFE, 0x03, 0xFC, 0x00, 0xFF, 0x81,
    0x7F, 0x80, 0x3F, 0xF8, 0xDF, 0xF0, 0x1F, 0xFF, 0xE3, 0xFF, 0x0E, 0xFF,
    0xF8, 0xFF, 0xFE, 0x1F, 0xFC, 0x0F, 0xFE, 0x03, 0xFE, 0x00, 0xFE, 0x00,
    0x3E, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xEE, 0x73, 0x9C, 0xE2, 0x00, 0x00,
    0x00, 0x03, 0x00, 0x60, 0x1C, 0x03, 0x80, 0x70, 0x06, 0x00, 0xE0, 0x1C,
    0x01, 0xC0, 0x3C, 0x03, 0xC0, 0x78, 0x07, 0x80, 0x78, 0x07, 0x80, 0xF8,
    0x0F, 0x80, 0xF8, 0x0F, 0x80, 0xF8, 0x0F, 0x80, 0xF8, 0x0F, 0x80, 0xF8,
    0x0F, 0x80, 0x78, 0x07, 0x80, 0x78, 0x03, 0xC0, 0x3C, 0x01, 0xC0, 0x1C,
    0x00, 0xE0, 0x0E, 0x00, 0x70, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x03, 0x00,
    0x10, 0x00, 0x0C, 0x00, 0x60, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x06, 0x00,
    0x70, 0x03, 0x80, 0x38, 0x03, 0xC0, 0x3C, 0x03, 0xE0, 0x1E, 0x01, 0xE0,
    0x1E, 0x01, 0xF0, 0x1F, 0x01, 0xF0, 0x1F, 0x01, 0xF0, 0x1F, 0x01, 0xF0,
    0x1F, 0x01, 0xF0, 0x1F, 0x01, 0xE0, 0x1E, 0x01, 0xE0, 0x3C, 0x03, 0xC0,
    0x38, 0x03, 0x80, 0x70, 0x07, 0x00, 0xE0, 0x0C, 0x01, 0x80, 0x30, 0x0C,
    0x00, 0x80, 0x00, 0x01, 0xC0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x80,
    0x03, 0xE0, 0x3C, 0x78, 0xEF, 0x9C, 0x7B, 0xF7, 0x3F, 0xFE, 0xDF, 0x8F,
    0xFF, 0xC0, 0x7F, 0x00, 0x3F, 0xC0, 0x7E, 0xBF, 0x3F, 0x77, 0xEF, 0x9C,
    0xFF, 0xC7, 0x1E, 0x63, 0xE3, 0x80, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x80,
    0x01, 0xC0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07, 0x80, 0x00, 0x01, 0xE0,
    0x00, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07, 0x80, 0x00, 0x01,
    0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07, 0x80, 0x0F,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07,
    0x80, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00, 0x00,
    0x07, 0x80, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00,
    0x00, 0x3C, 0x7E, 0xFE, 0xFF, 0xFF, 0xFF, 0x7F, 0x07, 0x06, 0x06, 0x0C,
    0x18, 0x30, 0x60, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x3C,
    0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x3C, 0x00, 0x1E, 0x00, 0x7C, 0x00,
    0xF0, 0x01, 0xE0, 0x07, 0xC0, 0x0F, 0x00, 0x1E, 0x00, 0x7C, 0x00, 0xF0,
    0x01, 0xE0, 0x07, 0xC0, 0x0F, 0x00, 0x1E, 0x00, 0x7C, 0x00, 0xF0, 0x01,
    0xE0, 0x07, 0xC0, 0x0F, 0x00, 0x1E, 0x00, 0x7C, 0x00, 0xF0, 0x01, 0xE0,
    0x03, 0xC0, 0x0F, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0xF0, 0x01, 0xE0, 0x03,
    0xC0, 0x0F, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0xF0, 0x00, 0x00, 0xFC, 0x00,
    0x0F, 0x3C, 0x00, 0x78, 0x78, 0x03, 0xE1, 0xF0, 0x1F, 0x03, 0xE0, 0x7C,
    0x0F, 0x83, 0xF0, 0x3F, 0x0F, 0xC0, 0xFC, 0x7F, 0x03, 0xF9, 0xFC, 0x0F,
    0xE7, 0xF0, 0x3F, 0xBF, 0xC0, 0xFE, 0xFF, 0x03, 0xFF, 0xFC, 0x0F, 0xFF,
    0xF0, 0x3F, 0xFF, 0xC0, 0xFF, 0xFF, 0x03, 0xFF, 0xFC, 0x0F, 0xFF, 0xF0,
    0x3F, 0xFF, 0xC0, 0xFF, 0xFF, 0x03, 0xFF, 0xFC, 0x0F, 0xFF, 0xF0, 0x3F,
    0x9F, 0xC0, 0xFE, 0x7F, 0x03, 0xF9, 0xFC, 0x0F, 0xE3, 0xF0, 0x3F, 0x0F,
    0xC0, 0xFC, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x80, 0xF8, 0x7C, 0x01, 0xE1,
    0xE0, 0x03, 0xCF, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x18, 0x00, 0x1E, 0x00,
    0x1F, 0x80, 0x1F, 0xE0, 0x1F, 0xF8, 0x1D, 0xFE, 0x00, 0x3F, 0x80, 0x0F,
    0xE0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF8,
    0x00, 0xFE, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF8, 0x00, 0xFE, 0x00,
    0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x3F, 0x80, 0x0F,
    0xE0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF8,
    0x00, 0xFE, 0x00, 0x7F, 0x80, 0x3F, 0xF8, 0xFF, 0xFF, 0xC0, 0x00, 0xFC,
    0x00, 0x1F, 0xF8, 0x03, 0xFF, 0xE0, 0x3F, 0xFF, 0x81, 0xFF, 0xFC, 0x1C,
    0x1F, 0xF1, 0xC0, 0x7F, 0x8C, 0x01, 0xFC, 0x40, 0x0F, 0xE0, 0x00, 0x3F,
    0x00, 0x01, 0xF8, 0x00, 0x0F, 0xC0, 0x00, 0x7C, 0x00, 0x03, 0xE0, 0x00,
    0x3E, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00, 0x07, 0x00,
    0x00, 0x70, 0x00, 0x07, 0x80, 0x00, 0x38, 0x00, 0x03, 0x80, 0x00, 0x38,
    0x01, 0x03, 0x80, 0x18, 0x38, 0x00, 0x81, 0x80, 0x1C, 0x1F, 0xFF, 0xE1,
    0xFF, 0xFF, 0x1F, 0xFF, 0xF9, 0xFF, 0xFF, 0x9F, 0xFF, 0xFC, 0xFF, 0xFF,
    0xE0, 0x00, 0xFE, 0x00, 0x3F, 0xFC, 0x03, 0xFF, 0xF0, 0x30, 0xFF, 0xC2,
    0x01, 0xFE, 0x30, 0x0F, 0xF1, 0x00, 0x3F, 0x80, 0x01, 0xFC, 0x00, 0x0F,
    0xE0, 0x00, 0x7E, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x01, 0xF0, 0x00,
    0x1F, 0xC0, 0x03, 0xFF, 0x00, 0x3F, 0xFC, 0x00, 0x7F, 0xF0, 0x00, 0xFF,
    0x80, 0x03, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x3F, 0x80, 0x00, 0xFC, 0x00,
    0x07, 0xE0, 0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x07, 0x80, 0x00, 0x3C,
    0x00, 0x01, 0xC7, 0x80, 0x0E, 0x7F, 0x00, 0xE3, 0xFC, 0x06, 0x1F, 0xF8,
    0xE0, 0x7F, 0xFC, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x1E,
    0x00, 0x01, 0xF0, 0x00, 0x1F, 0x80, 0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x00,
    0xFF, 0x00, 0x0D, 0xF8, 0x00, 0xEF, 0xC0, 0x06, 0x7E, 0x00, 0x63, 0xF0,
    0x07, 0x1F, 0x80, 0x30, 0xFC, 0x03, 0x07, 0xE0, 0x38, 0x3F, 0x03, 0x81,
    0xF8, 0x18, 0x0F, 0xC1, 0xC0, 0x7E, 0x1C, 0x03, 0xF0, 0xC0, 0x1F, 0x8E,
    0x00, 0xFC, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xC0, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00,
    0x07, 0xE0, 0x00, 0x3F, 0x00, 0x01, 0xF8, 0x00, 0x0F, 0xC0, 0x07, 0xFF,
    0xF0, 0x7F, 0xFF, 0x0F, 0xFF, 0xE0, 0xFF, 0xFE, 0x0F, 0xFF, 0xE1, 0xFF,
    0xFC, 0x18, 0x00, 0x01, 0x80, 0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 0x3F,
    0x80, 0x03, 0xFF, 0x80, 0x7F, 0xFE, 0x07, 0xFF, 0xF0, 0x7F, 0xFF, 0x87,
    0xFF, 0xFC, 0x7F, 0xFF, 0xC0, 0x07, 0xFC, 0x00, 0x1F, 0xE0, 0x00, 0x7E,
    0x00, 0x03, 0xE0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00, 0x00,
    0xC0, 0x00, 0x0C, 0x78, 0x00, 0x8F, 0xE0, 0x18, 0xFF, 0x87, 0x0F, 0xFF,
    0xE0, 0x7F, 0xF8, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x38, 0x00, 0x1F, 0x00,
    0x07, 0xE0, 0x00, 0x7C, 0x00, 0x0F, 0xC0, 0x00, 0xFC, 0x00, 0x0F, 0xC0,
    0x00, 0xFC, 0x00, 0x0F, 0xE0, 0x00, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F,
    0x00, 0x07, 0xF8, 0x00, 0x3F, 0xFF, 0x01, 0xFF, 0xFE, 0x1F, 0xF1, 0xFC,
    0xFF, 0x07, 0xE7, 0xF8, 0x3F, 0xBF, 0xC1, 0xFD, 0xFE, 0x07, 0xFF, 0xF0,
    0x3F, 0xFF, 0x81, 0xFF, 0xFC, 0x0F, 0xFF, 0xE0, 0x7F, 0xFF, 0x03, 0xFB,
    0xF8, 0x1F, 0xDF, 0xC0, 0xFE, 0xFE, 0x07, 0xE3, 0xF0, 0x3F, 0x1F, 0xC1,
    0xF0, 0x7E, 0x0F, 0x01, 0xF0, 0xF8, 0x03, 0xC7, 0x00, 0x07, 0xE0, 0x00,
    0x3F, 0xFF, 0xF9, 0xFF, 0xFF, 0xDF, 0xFF, 0xFE, 0xFF, 0xFF, 0xE7, 0xFF,
    0xFF, 0x3F, 0xFF, 0xF9, 0x80, 0x07, 0x98, 0x00, 0x3C, 0xC0, 0x03, 0xE4,
    0x00, 0x1E, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x03,
    0xC0, 0x00, 0x3C, 0x00, 0x01, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0xF0, 0x00,
    0x07, 0x80, 0x00, 0x7C, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x01, 0xF0,
    0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x07, 0xC0, 0x00, 0x3C, 0x00, 0x01,
    0xE0, 0x00, 0x1F, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00,
    0x01, 0xFE, 0x00, 0x38, 0x7C, 0x07, 0x80, 0xF0, 0x78, 0x07, 0xC3, 0xC0,
    0x1F, 0x3E, 0x00, 0xF9, 0xF0, 0x07, 0xCF, 0xC0, 0x3E, 0x7E, 0x01, 0xF3,
    0xF8, 0x0F, 0x1F, 0xE0, 0xF8, 0x7F, 0xC7, 0x83, 0xFF, 0xF0, 0x0F, 0xFE,
    0x00, 0x7F, 0xFC, 0x01, 0xFF, 0xF0, 0x03, 0xFF, 0xC0, 0x1F, 0xFF, 0x03,
    0xBF, 0xFC, 0x7C, 0x7F, 0xE7, 0xC1, 0xFF, 0x3E, 0x07, 0xFF, 0xE0, 0x1F,
    0xFF, 0x00, 0x7F, 0xF8, 0x03, 0xFF, 0xC0, 0x0F, 0xFE, 0x00, 0x7F, 0xF0,
    0x03, 0xE7, 0x80, 0x1F, 0x3E, 0x01, 0xF0, 0xF8, 0x0F, 0x83, 0xE1, 0xF8,
    0x0F, 0xFF, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x1C, 0x3C, 0x00,
    0xF0, 0x78, 0x07, 0x81, 0xF8, 0x3E, 0x07, 0xE1, 0xF8, 0x0F, 0xC7, 0xE0,
    0x3F, 0x3F, 0x80, 0xFE, 0xFE, 0x03, 0xFB, 0xF8, 0x0F, 0xFF, 0xE0, 0x3F,
    0xFF, 0x80, 0xFF, 0xFE, 0x03, 0xFF, 0xF8, 0x0F, 0xFF, 0xE0, 0x3F, 0xDF,
    0xC0, 0xFF, 0x7F, 0x03, 0xFC, 0xFC, 0x0F, 0xF3, 0xFC, 0x7F, 0x83, 0xFF,
    0xFE, 0x07, 0xF7, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0xFF, 0x00, 0x03, 0xF8,
    0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x00, 0x03, 0xF8, 0x00, 0x0F, 0xC0, 0x00,
    0x7E, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x01, 0xF0, 0x00, 0x3F, 0x00,
    0x03, 0x80, 0x00, 0x00, 0x3C, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x3C,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x7E, 0xFF, 0xFF,
    0xFF, 0xFF, 0x7E, 0x3C, 0x3C, 0x3F, 0x3F, 0xDF, 0xEF, 0xF7, 0xF9, 0xF8,
    0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x7F, 0x7F,
    0xBF, 0xFF, 0xFF, 0xFB, 0xFC, 0xFE, 0x07, 0x03, 0x01, 0x81, 0x81, 0x81,
    0x83, 0x81, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00,
    0xFC, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0xC0, 0x01, 0xFF, 0x80, 0x01,
    0xFF, 0x80, 0x01, 0xFF, 0x80, 0x01, 0xFF, 0x80, 0x01, 0xFF, 0x80, 0x01,
    0xFF, 0x80, 0x01, 0xFF, 0x80, 0x00, 0xFF, 0x80, 0x00, 0x3F, 0xE0, 0x00,
    0x07, 0xFE, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x7F,
    0xE0, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x03, 0xFF, 0x00,
    0x00, 0x3F, 0xF0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x03,
    0xC0, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0,
    0xC0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x03, 0xFC, 0x00,
    0x00, 0xFF, 0xC0, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x0F,
    0xFE, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x7F, 0xE0,
    0x00, 0x07, 0xFE, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x1F, 0xF0, 0x00, 0x1F,
    0xF8, 0x00, 0x1F, 0xF8, 0x00, 0x1F, 0xF8, 0x00, 0x1F, 0xF8, 0x00, 0x1F,
    0xF8, 0x00, 0x1F, 0xF8, 0x00, 0x1F, 0xF8, 0x00, 0x3F, 0xF0, 0x00, 0x0F,
    0xF0, 0x00, 0x03, 0xF0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x30, 0x00, 0x00,
    0x00, 0x07, 0xF0, 0x07, 0xFF, 0x03, 0x87, 0xE1, 0xC0, 0xFC, 0xF0, 0x3F,
    0xBE, 0x07, 0xEF, 0xC1, 0xFF, 0xF0, 0x7F, 0xFC, 0x1F, 0xDF, 0x07, 0xF7,
    0x81, 0xFC, 0x00, 0xFE, 0x00, 0x3F, 0x80, 0x1F, 0xC0, 0x07, 0xE0, 0x03,
    0xE0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x18, 0x00, 0x04, 0x00, 0x01, 0x00,
    0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x3C, 0x00, 0x1F, 0x80, 0x0F, 0xF0, 0x03, 0xFC, 0x00, 0xFF, 0x00, 0x3F,
    0xC0, 0x07, 0xE0, 0x00, 0xF0, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x1F,
    0xFF, 0xC0, 0x00, 0x1F, 0x00, 0xF0, 0x00, 0x3E, 0x00, 0x1E, 0x00, 0x3C,
    0x00, 0x03, 0x80, 0x3C, 0x00, 0x00, 0xE0, 0x3C, 0x00, 0x00, 0x30, 0x3E,
    0x00, 0x00, 0x0C, 0x3E, 0x00, 0x3C, 0x37, 0x1F, 0x00, 0x7E, 0xF1, 0x9F,
    0x00, 0x7C, 0xF8, 0xCF, 0x80, 0x78, 0x7C, 0x37, 0xC0, 0x7C, 0x3C, 0x1F,
    0xC0, 0x3C, 0x1E, 0x0F, 0xE0, 0x3C, 0x0F, 0x07, 0xF0, 0x3E, 0x0F, 0x03,
    0xF8, 0x1E, 0x07, 0x81, 0xFC, 0x0F, 0x03, 0xC0, 0xFE, 0x0F, 0x03, 0xE0,
    0x7F, 0x07, 0x81, 0xE0, 0x6F, 0x83, 0xC1, 0xF0, 0x37, 0xC1, 0xE1, 0x78,
    0x31, 0xF0, 0xF9, 0xBC, 0x18, 0xF8, 0x3F, 0x9E, 0x38, 0x3C, 0x0F, 0x0F,
    0xF8, 0x1F, 0x00, 0x01, 0xF0, 0x07, 0x80, 0x00, 0x00, 0x03, 0xE0, 0x00,
    0x00, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x07, 0xC0,
    0x00, 0xC0, 0x01, 0xF8, 0x03, 0xE0, 0x00, 0x3F, 0xFF, 0xC0, 0x00, 0x03,
    0xFF, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00,
    0x03, 0x80, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00,
    0x07, 0xE0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00,
    0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x00,
    0x37, 0xF8, 0x00, 0x00, 0x33, 0xF8, 0x00, 0x00, 0x33, 0xFC, 0x00, 0x00,
    0x61, 0xFC, 0x00, 0x00, 0x61, 0xFE, 0x00, 0x00, 0xC1, 0xFE, 0x00, 0x00,
    0xC0, 0xFF, 0x00, 0x00, 0xC0, 0xFF, 0x00, 0x01, 0x80, 0x7F, 0x00, 0x01,
    0x80, 0x7F, 0x80, 0x03, 0x80, 0x7F, 0x80, 0x03, 0xFF, 0xFF, 0xC0, 0x03,
    0xFF, 0xFF, 0xC0, 0x07, 0x00, 0x3F, 0xC0, 0x06, 0x00, 0x1F, 0xE0, 0x0E,
    0x00, 0x1F, 0xE0, 0x0C, 0x00, 0x0F, 0xF0, 0x0C, 0x00, 0x0F, 0xF0, 0x1C,
    0x00, 0x0F, 0xF8, 0x1C, 0x00, 0x0F, 0xF8, 0x7E, 0x00, 0x0F, 0xFC, 0xFF,
    0x80, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xE0, 0x1F, 0xF8,
    0x7F, 0x00, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F, 0xC0, 0xFF, 0x01, 0xFE,
    0x0F, 0xF0, 0x1F, 0xE0, 0xFF, 0x01, 0xFE, 0x0F, 0xF0, 0x1F, 0xE0, 0xFF,
    0x01, 0xFE, 0x0F, 0xF0, 0x1F, 0xC0, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F,
    0x00, 0xFF, 0x0F, 0xC0, 0x0F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0xC0, 0x0F,
    0xF0, 0xFF, 0x00, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x1F, 0xE0, 0xFF, 0x01,
    0xFE, 0x0F, 0xF0, 0x0F, 0xF0, 0xFF, 0x00, 0xFF, 0x0F, 0xF0, 0x0F, 0xF0,
    0xFF, 0x00, 0xFF, 0x0F, 0xF0, 0x0F, 0xF0, 0xFF, 0x00, 0xFF, 0x0F, 0xF0,
    0x0F, 0xE0, 0xFF, 0x01, 0xFE, 0x0F, 0xF0, 0x1F, 0xC0, 0xFF, 0x87, 0xF0,
    0x3F, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0x00, 0x00, 0x0F, 0xF0, 0x08, 0x01,
    0xFF, 0xF0, 0x60, 0x0F, 0xC1, 0xF9, 0x80, 0xFC, 0x01, 0xFE, 0x07, 0xE0,
    0x01, 0xF8, 0x3F, 0x00, 0x03, 0xE1, 0xFC, 0x00, 0x07, 0x87, 0xE0, 0x00,
    0x1E, 0x3F, 0x80, 0x00, 0x38, 0xFE, 0x00, 0x00, 0x67, 0xF8, 0x00, 0x01,
    0x9F, 0xC0, 0x00, 0x02, 0x7F, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x0F,
    0xF0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x03, 0xFC,
    0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0xFF, 0x00,
    0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00,
    0x00, 0x7F, 0x80, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00,
    0x87, 0xF0, 0x00, 0x07, 0x0F, 0xE0, 0x00, 0x38, 0x1F, 0x80, 0x01, 0xC0,
    0x3F, 0x00, 0x1E, 0x00, 0x7F, 0x01, 0xE0, 0x00, 0x7F, 0xFF, 0x00, 0x00,
    0x3F, 0xE0, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xFE, 0x00, 0x0F,
    0xF8, 0x7F, 0x80, 0x0F, 0xF0, 0x1F, 0xC0, 0x0F, 0xF0, 0x0F, 0xF0, 0x0F,
    0xF0, 0x07, 0xF0, 0x0F, 0xF0, 0x03, 0xF8, 0x0F, 0xF0, 0x03, 0xFC, 0x0F,
    0xF0, 0x01, 0xFC, 0x0F, 0xF0, 0x01, 0xFE, 0x0F, 0xF0, 0x01, 0xFE, 0x0F,
    0xF0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F,
    0xF0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F,
    0xF0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F,
    0xF0, 0x00, 0xFE, 0x0F, 0xF0, 0x00, 0xFE, 0x0F, 0xF0, 0x01, 0xFE, 0x0F,
    0xF0, 0x01, 0xFC, 0x0F, 0xF0, 0x01, 0xFC, 0x0F, 0xF0, 0x03, 0xF8, 0x0F,
    0xF0, 0x03, 0xF0, 0x0F, 0xF0, 0x07, 0xE0, 0x0F, 0xF0, 0x0F, 0xC0, 0x0F,
    0xF8, 0x3F, 0x80, 0x1F, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0xFF,
    0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFC, 0x1F, 0xE0, 0x1F, 0xC1, 0xFE, 0x00,
    0x3C, 0x1F, 0xE0, 0x01, 0xC1, 0xFE, 0x00, 0x0C, 0x1F, 0xE0, 0x00, 0xC1,
    0xFE, 0x00, 0x04, 0x1F, 0xE0, 0x20, 0x41, 0xFE, 0x02, 0x00, 0x1F, 0xE0,
    0x60, 0x01, 0xFE, 0x06, 0x00, 0x1F, 0xE0, 0xE0, 0x01, 0xFE, 0x1E, 0x00,
    0x1F, 0xFF, 0xE0, 0x01, 0xFF, 0xFE, 0x00, 0x1F, 0xE3, 0xE0, 0x01, 0xFE,
    0x0E, 0x00, 0x1F, 0xE0, 0x60, 0x01, 0xFE, 0x06, 0x00, 0x1F, 0xE0, 0x20,
    0x01, 0xFE, 0x02, 0x00, 0x1F, 0xE0, 0x00, 0x11, 0xFE, 0x00, 0x03, 0x1F,
    0xE0, 0x00, 0x71, 0xFE, 0x00, 0x07, 0x1F, 0xE0, 0x00, 0xE1, 0xFE, 0x00,
    0x1E, 0x1F, 0xE0, 0x03, 0xE3, 0xFF, 0x01, 0xFE, 0xFF, 0xFF, 0xFF, 0xEF,
    0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0x9F, 0xFF, 0xFF, 0xC7, 0xFC, 0x07,
    0xE3, 0xFC, 0x00, 0xF1, 0xFE, 0x00, 0x38, 0xFF, 0x00, 0x0C, 0x7F, 0x80,
    0x06, 0x3F, 0xC0, 0x01, 0x1F, 0xE0, 0x20, 0x8F, 0xF0, 0x10, 0x07, 0xF8,
    0x18, 0x03, 0xFC, 0x0C, 0x01, 0xFE, 0x0E, 0x00, 0xFF, 0x1F, 0x00, 0x7F,
    0xFF, 0x80, 0x3F, 0xFF, 0xC0, 0x1F, 0xE3, 0xE0, 0x0F, 0xF0, 0x70, 0x07,
    0xF8, 0x18, 0x03, 0xFC, 0x0C, 0x01, 0xFE, 0x02, 0x00, 0xFF, 0x01, 0x00,
    0x7F, 0x80, 0x00, 0x3F, 0xC0, 0x00, 0x1F, 0xE0, 0x00, 0x0F, 0xF0, 0x00,
    0x07, 0xF8, 0x00, 0x03, 0xFC, 0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF, 0x00,
    0x00, 0xFF, 0xC0, 0x01, 0xFF, 0xFC, 0x00, 0x00, 0x0F, 0xF0, 0x08, 0x00,
    0x3F, 0xFE, 0x0C, 0x00, 0x3F, 0x07, 0xC6, 0x00, 0x7E, 0x00, 0xFF, 0x00,
    0x7E, 0x00, 0x1F, 0x80, 0x7E, 0x00, 0x07, 0xC0, 0x7F, 0x00, 0x01, 0xE0,
    0x3F, 0x00, 0x00, 0x70, 0x3F, 0x80, 0x00, 0x38, 0x1F, 0xC0, 0x00, 0x0C,
    0x1F, 0xE0, 0x00, 0x06, 0x0F, 0xE0, 0x00, 0x01, 0x07, 0xF0, 0x00, 0x00,
    0x07, 0xF8, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00,
    0x00, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x3F, 0xC0, 0x00,
    0x00, 0x1F, 0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x03, 0xFF, 0xFF, 0xF8, 0x00,
    0x3F, 0xF1, 0xFC, 0x00, 0x0F, 0xF0, 0xFF, 0x00, 0x07, 0xF8, 0x7F, 0x80,
    0x03, 0xFC, 0x1F, 0xC0, 0x01, 0xFE, 0x0F, 0xE0, 0x00, 0xFF, 0x03, 0xF8,
    0x00, 0x7F, 0x80, 0xFC, 0x00, 0x3F, 0xC0, 0x3F, 0x00, 0x1F, 0xE0, 0x0F,
    0xC0, 0x0F, 0xF0, 0x03, 0xF8, 0x1F, 0xF0, 0x00, 0x7F, 0xFF, 0xC0, 0x00,
    0x07, 0xFE, 0x00, 0x00, 0xFF, 0xFC, 0x1F, 0xFF, 0x9F, 0xF8, 0x03, 0xFF,
    0x07, 0xF8, 0x00, 0xFF, 0x03, 0xFC, 0x00, 0x7F, 0x81, 0xFE, 0x00, 0x3F,
    0xC0, 0xFF, 0x00, 0x1F, 0xE0, 0x7F, 0x80, 0x0F, 0xF0, 0x3F, 0xC0, 0x07,
    0xF8, 0x1F, 0xE0, 0x03, 0xFC, 0x0F, 0xF0, 0x01, 0xFE, 0x07, 0xF8, 0x00,
    0xFF, 0x03, 0xFC, 0x00, 0x7F, 0x81, 0xFE, 0x00, 0x3F, 0xC0, 0xFF, 0x00,
    0x1F, 0xE0, 0x7F, 0x80, 0x0F, 0xF0, 0x3F, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF,
    0xFF, 0xFC, 0x0F, 0xF0, 0x01, 0xFE, 0x07, 0xF8, 0x00, 0xFF, 0x03, 0xFC,
    0x00, 0x7F, 0x81, 0xFE, 0x00, 0x3F, 0xC0, 0xFF, 0x00, 0x1F, 0xE0, 0x7F,
    0x80, 0x0F, 0xF0, 0x3F, 0xC0, 0x07, 0xF8, 0x1F, 0xE0, 0x03, 0xFC, 0x0F,
    0xF0, 0x01, 0xFE, 0x07, 0xF8, 0x00, 0xFF, 0x03, 0xFC, 0x00, 0x7F, 0x81,
    0xFE, 0x00, 0x3F, 0xC0, 0xFF, 0x00, 0x1F, 0xE0, 0xFF, 0xC0, 0x1F, 0xF9,
    0xFF, 0xF8, 0x3F, 0xFF, 0xFF, 0xFE, 0x7F, 0xE0, 0x7F, 0x80, 0xFF, 0x01,
    0xFE, 0x03, 0xFC, 0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F,
    0x80, 0xFF, 0x01, 0xFE, 0x03, 0xFC, 0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xE0,
    0x3F, 0xC0, 0x7F, 0x80, 0xFF, 0x01, 0xFE, 0x03, 0xFC, 0x07, 0xF8, 0x0F,
    0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F, 0x80, 0xFF, 0x01, 0xFE, 0x03, 0xFC,
    0x0F, 0xFC, 0x7F, 0xFF, 0x01, 0xFF, 0xFC, 0x00, 0xFF, 0xC0, 0x01, 0xFE,
    0x00, 0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00,
    0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07,
    0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8,
    0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8, 0x00,
    0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8, 0x00, 0x1F,
    0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8, 0x78, 0x1F, 0xE3,
    0xF0, 0x7F, 0x8F, 0xC1, 0xFC, 0x3F, 0x07, 0xF0, 0xFC, 0x1F, 0xC1, 0xE0,
    0xFE, 0x07, 0xC3, 0xF0, 0x0F, 0xFF, 0x80, 0x07, 0xF0, 0x00, 0xFF, 0xFC,
    0x1F, 0xFF, 0x0F, 0xFC, 0x00, 0xFF, 0x01, 0xFE, 0x00, 0x1E, 0x00, 0x7F,
    0x80, 0x07, 0x00, 0x1F, 0xE0, 0x03, 0x80, 0x07, 0xF8, 0x01, 0xC0, 0x01,
    0xFE, 0x00, 0xE0, 0x00, 0x7F, 0x80, 0x70, 0x00, 0x1F, 0xE0, 0x38, 0x00,
    0x07, 0xF8, 0x1C, 0x00, 0x01, 0xFE, 0x0E, 0x00, 0x00, 0x7F, 0x87, 0x00,
    0x00, 0x1F, 0xE3, 0xC0, 0x00, 0x07, 0xF9, 0xF8, 0x00, 0x01, 0xFE, 0xFE,
    0x00, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x07, 0xFD,
    0xFF, 0x00, 0x01, 0xFE, 0x7F, 0xE0, 0x00, 0x7F, 0x8F, 0xF8, 0x00, 0x1F,
    0xE1, 0xFF, 0x00, 0x07, 0xF8, 0x3F, 0xE0, 0x01, 0xFE, 0x07, 0xFC, 0x00,
    0x7F, 0x81, 0xFF, 0x80, 0x1F, 0xE0, 0x3F, 0xE0, 0x07, 0xF8, 0x07, 0xFC,
    0x01, 0xFE, 0x00, 0xFF, 0x80, 0x7F, 0x80, 0x1F, 0xF0, 0x1F, 0xE0, 0x07,
    0xFE, 0x07, 0xF8, 0x00, 0xFF, 0x83, 0xFF, 0x00, 0x3F, 0xF3, 0xFF, 0xF0,
    0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x1F, 0xE0, 0x00,
    0x01, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F,
    0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00,
    0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01,
    0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0xE0,
    0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00,
    0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x01, 0x1F, 0xE0, 0x00, 0x31, 0xFE,
    0x00, 0x03, 0x1F, 0xE0, 0x00, 0x71, 0xFE, 0x00, 0x07, 0x1F, 0xE0, 0x00,
    0xE1, 0xFE, 0x00, 0x1E, 0x1F, 0xE0, 0x07, 0xE3, 0xFF, 0x01, 0xFE, 0xFF,
    0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFE, 0x7F, 0xF0, 0x00, 0x01, 0xFF, 0xE1,
    0xFF, 0x00, 0x00, 0x3F, 0xF0, 0x1F, 0xE0, 0x00, 0x0F, 0xFC, 0x03, 0xFC,
    0x00, 0x01, 0xFF, 0x80, 0x7F, 0xC0, 0x00, 0x2F, 0xF0, 0x0B, 0xF8, 0x00,
    0x0D, 0xFE, 0x01, 0x7F, 0x80, 0x01, 0xBF, 0xC0, 0x27, 0xF0, 0x00, 0x67,
    0xF8, 0x04, 0xFF, 0x00, 0x0C, 0xFF, 0x00, 0x8F, 0xE0, 0x03, 0x1F, 0xE0,
    0x11, 0xFE, 0x00, 0x63, 0xFC, 0x02, 0x3F, 0xC0, 0x08, 0x7F, 0x80, 0x43,
    0xF8, 0x03, 0x0F, 0xF0, 0x08, 0x7F, 0x80, 0x61, 0xFE, 0x01, 0x07, 0xF0,
    0x18, 0x3F, 0xC0, 0x20, 0xFF, 0x03, 0x07, 0xF8, 0x04, 0x0F, 0xE0, 0xC0,
    0xFF, 0x00, 0x81, 0xFE, 0x18, 0x1F, 0xE0, 0x10, 0x3F, 0xC6, 0x03, 0xFC,
    0x02, 0x03, 0xF8, 0xC0, 0x7F, 0x80, 0x40, 0x7F, 0x98, 0x0F, 0xF0, 0x08,
    0x07, 0xF6, 0x01, 0xFE, 0x01, 0x00, 0xFF, 0xC0, 0x3F, 0xC0, 0x20, 0x0F,
    0xF0, 0x07, 0xF8, 0x04, 0x01, 0xFE, 0x00, 0xFF, 0x00, 0x80, 0x1F, 0x80,
    0x1F, 0xE0, 0x10, 0x03, 0xF0, 0x03, 0xFC, 0x02, 0x00, 0x7E, 0x00, 0x7F,
    0x80, 0x40, 0x07, 0x80, 0x0F, 0xF0, 0x0C, 0x00, 0xF0, 0x01, 0xFE, 0x07,
    0xC0, 0x0C, 0x00, 0x7F, 0xE7, 0xFF, 0x01, 0x80, 0x3F, 0xFF, 0xFF, 0xC0,
    0x03, 0xFE, 0xFF, 0xC0, 0x01, 0xF0, 0xFF, 0xC0, 0x01, 0xC0, 0xFF, 0xC0,
    0x01, 0x80, 0xFF, 0x80, 0x03, 0x01, 0xFF, 0x80, 0x06, 0x03, 0xFF, 0x80,
    0x0C, 0x07, 0xFF, 0x80, 0x18, 0x0D, 0xFF, 0x80, 0x30, 0x19, 0xFF, 0x00,
    0x60, 0x31, 0xFF, 0x00, 0xC0, 0x61, 0xFF, 0x01, 0x80, 0xC1, 0xFF, 0x03,
    0x01, 0x83, 0xFF, 0x06, 0x03, 0x03, 0xFE, 0x0C, 0x06, 0x03, 0xFE, 0x18,
    0x0C, 0x03, 0xFE, 0x30, 0x18, 0x03, 0xFE, 0x60, 0x30, 0x03, 0xFE, 0xC0,
    0x60, 0x07, 0xFD, 0x80, 0xC0, 0x07, 0xFF, 0x01, 0x80, 0x07, 0xFE, 0x03,
    0x00, 0x07, 0xFC, 0x06, 0x00, 0x07, 0xF8, 0x0C, 0x00, 0x07, 0xF0, 0x18,
    0x00, 0x0F, 0xE0, 0x30, 0x00, 0x0F, 0xC0, 0x60, 0x00, 0x0F, 0x80, 0xC0,
    0x00, 0x0F, 0x01, 0xC0, 0x00, 0x0E, 0x0F, 0xC0, 0x00, 0x1C, 0x7F, 0xE0,
    0x00, 0x18, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x3F,
    0x07, 0xF0, 0x00, 0x7E, 0x00, 0xFC, 0x00, 0x7E, 0x00, 0x3F, 0x00, 0x7E,
    0x00, 0x1F, 0xC0, 0x7F, 0x00, 0x07, 0xF0, 0x3F, 0x00, 0x03, 0xF8, 0x3F,
    0x80, 0x00, 0xFE, 0x3F, 0xC0, 0x00, 0x7F, 0x1F, 0xE0, 0x00, 0x3F, 0xCF,
    0xE0, 0x00, 0x0F, 0xEF, 0xF0, 0x00, 0x07, 0xF7, 0xF8, 0x00, 0x03, 0xFF,
    0xFC, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x7F,
    0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x0F,
    0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x03, 0xFD, 0xFC, 0x00, 0x01,
    0xFC, 0xFE, 0x00, 0x01, 0xFE, 0x7F, 0x80, 0x00, 0xFF, 0x1F, 0xC0, 0x00,
    0x7F, 0x0F, 0xE0, 0x00, 0x3F, 0x83, 0xF8, 0x00, 0x3F, 0x80, 0xFC, 0x00,
    0x1F, 0x80, 0x3F, 0x00, 0x1F, 0x80, 0x0F, 0xC0, 0x1F, 0x80, 0x03, 0xF8,
    0x3F, 0x80, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0xFF,
    0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0x01, 0xFE, 0x1F, 0xE0, 0x7F, 0x81, 0xFC,
    0x1F, 0xE0, 0x7F, 0x87, 0xF8, 0x0F, 0xE1, 0xFE, 0x03, 0xFC, 0x7F, 0x80,
    0xFF, 0x1F, 0xE0, 0x3F, 0xC7, 0xF8, 0x0F, 0xF1, 0xFE, 0x03, 0xFC, 0x7F,
    0x80, 0xFF, 0x1F, 0xE0, 0x3F, 0x87, 0xF8, 0x1F, 0xE1, 0xFE, 0x07, 0xF0,
    0x7F, 0x87, 0xF8, 0x1F, 0xFF, 0xF8, 0x07, 0xFF, 0xF8, 0x01, 0xFE, 0x00,
    0x00, 0x7F, 0x80, 0x00, 0x1F, 0xE0, 0x00, 0x07, 0xF8, 0x00, 0x01, 0xFE,
    0x00, 0x00, 0x7F, 0x80, 0x00, 0x1F, 0xE0, 0x00, 0x07, 0xF8, 0x00, 0x01,
    0xFE, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x1F, 0xE0, 0x00, 0x07, 0xF8, 0x00,
    0x03, 0xFF, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
    0x3F, 0xFF, 0x80, 0x00, 0x3F, 0x07, 0xE0, 0x00, 0x7E, 0x00, 0xFC, 0x00,
    0x7E, 0x00, 0x3F, 0x00, 0x7E, 0x00, 0x1F, 0xC0, 0x7F, 0x00, 0x07, 0xF0,
    0x3F, 0x00, 0x03, 0xF8, 0x3F, 0x80, 0x00, 0xFE, 0x1F, 0xC0, 0x00, 0x7F,
    0x1F, 0xE0, 0x00, 0x3F, 0xCF, 0xE0, 0x00, 0x0F, 0xE7, 0xF0, 0x00, 0x07,
    0xF7, 0xF8, 0x00, 0x03, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00,
    0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xC0, 0x00,
    0x1F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xF8, 0x00,
    0x03, 0xFD, 0xFC, 0x00, 0x01, 0xFC, 0xFE, 0x00, 0x01, 0xFE, 0x7F, 0x80,
    0x00, 0xFF, 0x1F, 0xC0, 0x00, 0x7F, 0x0F, 0xE0, 0x00, 0x3F, 0x83, 0xF8,
    0x00, 0x3F, 0x80, 0xFC, 0x00, 0x1F, 0x80, 0x3F, 0x00, 0x1F, 0x80, 0x0F,
    0xC0, 0x1F, 0x80, 0x03, 0xF0, 0x1F, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00,
    0x0F, 0xFC, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00,
    0x00, 0x7F, 0xF0, 0x00, 0x00, 0x1F, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0x80,
    0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0xFF, 0xFF, 0xE0,
    0x00, 0xFF, 0xFF, 0xF8, 0x00, 0x7F, 0xC3, 0xFC, 0x00, 0xFF, 0x01, 0xFC,
    0x01, 0xFE, 0x03, 0xFC, 0x03, 0xFC, 0x03, 0xF8, 0x07, 0xF8, 0x07, 0xF8,
    0x0F, 0xF0, 0x0F, 0xF0, 0x1F, 0xE0, 0x1F, 0xE0, 0x3F, 0xC0, 0x3F, 0xC0,
    0x7F, 0x80, 0x7F, 0x80, 0xFF, 0x00, 0xFF, 0x01, 0xFE, 0x01, 0xFC, 0x03,
    0xFC, 0x07, 0xF8, 0x07, 0xF8, 0x1F, 0xE0, 0x0F, 0xF0, 0xFF, 0x00, 0x1F,
    0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x7F, 0x9F, 0xE0, 0x00, 0xFF,
    0x3F, 0xC0, 0x01, 0xFE, 0x3F, 0xC0, 0x03, 0xFC, 0x7F, 0xC0, 0x07, 0xF8,
    0x7F, 0xC0, 0x0F, 0xF0, 0x7F, 0x80, 0x1F, 0xE0, 0xFF, 0x80, 0x3F, 0xC0,
    0xFF, 0x80, 0x7F, 0x80, 0xFF, 0x00, 0xFF, 0x01, 0xFF, 0x01, 0xFE, 0x01,
    0xFF, 0x03, 0xFC, 0x01, 0xFF, 0x0F, 0xFC, 0x03, 0xFE, 0x7F, 0xFE, 0x03,
    0xFF, 0x03, 0xF8, 0x10, 0x7F, 0xF9, 0x87, 0xC1, 0xFC, 0x78, 0x03, 0xE7,
    0x80, 0x0F, 0x3C, 0x00, 0x3B, 0xE0, 0x01, 0xDF, 0x00, 0x06, 0xF8, 0x00,
    0x37, 0xE0, 0x00, 0xBF, 0x80, 0x01, 0xFF, 0x00, 0x0F, 0xFE, 0x00, 0x3F,
    0xFC, 0x01, 0xFF, 0xF8, 0x07, 0xFF, 0xF0, 0x1F, 0xFF, 0xC0, 0x7F, 0xFF,
    0x00, 0xFF, 0xFC, 0x01, 0xFF, 0xE0, 0x03, 0xFF, 0x80, 0x07, 0xFC, 0x00,
    0x1F, 0xF0, 0x00, 0x3F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF0, 0x00, 0x3F,
    0xC0, 0x01, 0xEE, 0x00, 0x0F, 0x78, 0x00, 0xF3, 0xE0, 0x0F, 0x9F, 0xC0,
    0xF8, 0x8F, 0xFF, 0x04, 0x0F, 0xE0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFC, 0x3F, 0xC3, 0xFF, 0x03, 0xFC, 0x0F, 0xE0, 0x3F, 0xC0,
    0x7C, 0x03, 0xFC, 0x03, 0xC0, 0x3F, 0xC0, 0x38, 0x03, 0xFC, 0x01, 0x80,
    0x3F, 0xC0, 0x10, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFC,
    0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00,
    0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F,
    0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFC, 0x00,
    0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03,
    0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0,
    0x00, 0x03, 0xFC, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x3F, 0xFF, 0xC0, 0xFF,
    0xFE, 0x07, 0xFC, 0xFF, 0xC0, 0x07, 0xC1, 0xFE, 0x00, 0x0E, 0x07, 0xF8,
    0x00, 0x18, 0x1F, 0xE0, 0x00, 0x60, 0x7F, 0x80, 0x01, 0x81, 0xFE, 0x00,
    0x06, 0x07, 0xF8, 0x00, 0x18, 0x1F, 0xE0, 0x00, 0x60, 0x7F, 0x80, 0x01,
    0x81, 0xFE, 0x00, 0x06, 0x07, 0xF8, 0x00, 0x18, 0x1F, 0xE0, 0x00, 0x60,
    0x7F, 0x80, 0x01, 0x81, 0xFE, 0x00, 0x06, 0x07, 0xF8, 0x00, 0x18, 0x1F,
    0xE0, 0x00, 0x60, 0x7F, 0x80, 0x01, 0x81, 0xFE, 0x00, 0x06, 0x07, 0xF8,
    0x00, 0x18, 0x1F, 0xE0, 0x00, 0x60, 0x7F, 0x80, 0x01, 0x81, 0xFE, 0x00,
    0x06, 0x07, 0xF8, 0x00, 0x18, 0x1F, 0xE0, 0x00, 0x60, 0x7F, 0x80, 0x03,
    0x00, 0xFF, 0x00, 0x0C, 0x03, 0xFC, 0x00, 0x30, 0x07, 0xF0, 0x01, 0x80,
    0x0F, 0xE0, 0x0E, 0x00, 0x1F, 0xE0, 0xF0, 0x00, 0x1F, 0xFF, 0x00, 0x00,
    0x1F, 0xF0, 0x00, 0xFF, 0xFF, 0x01, 0xFF, 0x9F, 0xFC, 0x00, 0x1F, 0x07,
    0xFC, 0x00, 0x07, 0x01, 0xFE, 0x00, 0x03, 0x00, 0x7F, 0x80, 0x03, 0x80,
    0x3F, 0xC0, 0x01, 0x80, 0x1F, 0xE0, 0x00, 0xC0, 0x07, 0xF8, 0x00, 0xC0,
    0x03, 0xFC, 0x00, 0x60, 0x00, 0xFF, 0x00, 0x30, 0x00, 0x7F, 0x80, 0x30,
    0x00, 0x1F, 0xE0, 0x18, 0x00, 0x0F, 0xF0, 0x18, 0x00, 0x07, 0xF8, 0x0C,
    0x00, 0x01, 0xFE, 0x06, 0x00, 0x00, 0xFF, 0x06, 0x00, 0x00, 0x3F, 0xC3,
    0x00, 0x00, 0x1F, 0xE3, 0x80, 0x00, 0x0F, 0xF1, 0x80, 0x00, 0x03, 0xFC,
    0xC0, 0x00, 0x01, 0xFE, 0xC0, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x00, 0x3F,
    0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x03,
    0xF8, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00,
    0x1E, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
    0x01, 0x80, 0x00, 0xFF, 0xF8, 0x7F, 0xFF, 0x0F, 0xFB, 0xFF, 0x00, 0xFF,
    0xC0, 0x1F, 0x0F, 0xF0, 0x03, 0xFC, 0x00, 0x70, 0x3F, 0x80, 0x0F, 0xE0,
    0x03, 0x81, 0xFE, 0x00, 0x7F, 0x80, 0x1C, 0x0F, 0xF0, 0x03, 0xFC, 0x00,
    0xC0, 0x3F, 0x80, 0x0F, 0xE0, 0x06, 0x01, 0xFE, 0x00, 0x7F, 0x00, 0x70,
    0x0F, 0xF0, 0x07, 0xFC, 0x03, 0x00, 0x3F, 0x80, 0x3F, 0xE0, 0x18, 0x01,
    0xFE, 0x01, 0xFF, 0x01, 0xC0, 0x0F, 0xF0, 0x1B, 0xFC, 0x0C, 0x00, 0x3F,
    0x80, 0xCF, 0xE0, 0x60, 0x01, 0xFE, 0x06, 0x7F, 0x07, 0x00, 0x0F, 0xF0,
    0x63, 0xFC, 0x30, 0x00, 0x3F, 0x83, 0x0F, 0xE1, 0x80, 0x01, 0xFE, 0x30,
    0x7F, 0x1C, 0x00, 0x07, 0xF1, 0x81, 0xFC, 0xC0, 0x00, 0x3F, 0x8C, 0x0F,
    0xE6, 0x00, 0x01, 0xFE, 0xC0, 0x7F, 0x70, 0x00, 0x07, 0xF6, 0x01, 0xFB,
    0x00, 0x00, 0x3F, 0xE0, 0x0F, 0xF8, 0x00, 0x01, 0xFF, 0x00, 0x7F, 0xC0,
    0x00, 0x07, 0xF8, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x00,
    0x01, 0xFC, 0x00, 0x7F, 0x00, 0x00, 0x07, 0xE0, 0x01, 0xF0, 0x00, 0x00,
    0x3E, 0x00, 0x0F, 0x80, 0x00, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x00, 0x07,
    0x00, 0x01, 0xC0, 0x00, 0x00, 0x38, 0x00, 0x0E, 0x00, 0x00, 0x01, 0xC0,
    0x00, 0x70, 0x00, 0x00, 0x04, 0x00, 0x01, 0x00, 0x00, 0xFF, 0xFF, 0x0F,
    0xFF, 0x3F, 0xF8, 0x01, 0xF8, 0x1F, 0xF8, 0x01, 0xE0, 0x0F, 0xF8, 0x01,
    0xC0, 0x0F, 0xF8, 0x01, 0x80, 0x07, 0xFC, 0x03, 0x80, 0x03, 0xFE, 0x07,
    0x00, 0x03, 0xFE, 0x06, 0x00, 0x01, 0xFF, 0x0C, 0x00, 0x00, 0xFF, 0x9C,
    0x00, 0x00, 0xFF, 0x98, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00, 0x3F, 0xF0,
    0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x0F, 0xF0,
    0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xFC,
    0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x19, 0xFE, 0x00, 0x00, 0x31, 0xFF,
    0x00, 0x00, 0x70, 0xFF, 0x80, 0x00, 0x60, 0x7F, 0x80, 0x00, 0xC0, 0x7F,
    0xC0, 0x01, 0xC0, 0x3F, 0xE0, 0x03, 0x80, 0x1F, 0xE0, 0x07, 0x00, 0x1F,
    0xF0, 0x07, 0x00, 0x0F, 0xF8, 0x0F, 0x00, 0x0F, 0xF8, 0x3F, 0x80, 0x1F,
    0xFC, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0x7F, 0xF0, 0x00,
    0x7E, 0x1F, 0xF0, 0x00, 0x38, 0x1F, 0xF0, 0x00, 0x38, 0x0F, 0xF0, 0x00,
    0x70, 0x0F, 0xF8, 0x00, 0x60, 0x07, 0xF8, 0x00, 0x60, 0x07, 0xFC, 0x00,
    0xC0, 0x03, 0xFC, 0x01, 0xC0, 0x01, 0xFE, 0x01, 0x80, 0x01, 0xFE, 0x03,
    0x00, 0x00, 0xFF, 0x03, 0x00, 0x00, 0xFF, 0x86, 0x00, 0x00, 0x7F, 0x8E,
    0x00, 0x00, 0x7F, 0xCC, 0x00, 0x00, 0x3F, 0xD8, 0x00, 0x00, 0x3F, 0xF8,
    0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x0F, 0xF0,
    0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x0F, 0xF0,
    0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x0F, 0xF0,
    0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x0F, 0xF0,
    0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x00, 0x7F, 0xFE,
    0x00, 0x3F, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFC, 0x3F, 0x80, 0x7F, 0xC3,
    0xE0, 0x07, 0xF8, 0x38, 0x00, 0xFF, 0x83, 0x80, 0x0F, 0xF0, 0x30, 0x01,
    0xFE, 0x07, 0x00, 0x3F, 0xE0, 0x60, 0x03, 0xFC, 0x06, 0x00, 0x7F, 0xC0,
    0x00, 0x0F, 0xF8, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x01,
    0xFE, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x7F, 0x80,
    0x00, 0x0F, 0xF8, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x03,
    0xFE, 0x00, 0x00, 0x3F, 0xC0, 0x01, 0x07, 0xFC, 0x00, 0x30, 0xFF, 0x80,
    0x03, 0x0F, 0xF0, 0x00, 0x31, 0xFF, 0x00, 0x07, 0x1F, 0xE0, 0x00, 0xF3,
    0xFE, 0x00, 0x1E, 0x7F, 0xC0, 0x03, 0xE7, 0xF8, 0x01, 0xFE, 0xFF, 0xFF,
    0xFF, 0xEF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xF0, 0x7C, 0x0F, 0x81,
    0xF0, 0x3E, 0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xF0,
    0x3E, 0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E,
    0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07,
    0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xFF, 0xFF, 0xF8, 0xF0,
    0x01, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0xC0, 0x07, 0x80, 0x0F, 0x00,
    0x1F, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x7C, 0x00, 0x78, 0x00, 0xF0, 0x01,
    0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1F,
    0x00, 0x1E, 0x00, 0x3C, 0x00, 0x78, 0x00, 0x78, 0x00, 0xF0, 0x01, 0xE0,
    0x01, 0xE0, 0x03, 0xC0, 0x07, 0x80, 0x07, 0x80, 0x0F, 0x00, 0x1E, 0x00,
    0x1E, 0xFF, 0xFF, 0xFC, 0x1F, 0x81, 0xF0, 0x3E, 0x07, 0xC0, 0xF8, 0x1F,
    0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07, 0xC0, 0xF8, 0x1F, 0x03,
    0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0,
    0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C,
    0x0F, 0x81, 0xF0, 0x3F, 0xFF, 0xFF, 0xF8, 0x00, 0x78, 0x00, 0x07, 0xC0,
    0x00, 0x3F, 0x00, 0x03, 0xF8, 0x00, 0x1F, 0xE0, 0x01, 0xEF, 0x00, 0x0F,
    0x3C, 0x00, 0xF1, 0xE0, 0x07, 0x87, 0x80, 0x78, 0x3C, 0x03, 0xC0, 0xF0,
    0x3C, 0x07, 0x81, 0xE0, 0x1E, 0x1E, 0x00, 0xF0, 0xF0, 0x07, 0xCF, 0x00,
    0x1E, 0x78, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0x70, 0x1F, 0x03, 0xF0, 0x7E, 0x03, 0xE0, 0x3E, 0x01, 0xE0, 0x1E,
    0x00, 0xE0, 0x03, 0xFC, 0x00, 0x3F, 0xFC, 0x03, 0xE1, 0xF8, 0x0F, 0x03,
    0xF0, 0x7C, 0x07, 0xC1, 0xF8, 0x1F, 0x87, 0xE0, 0x7E, 0x1F, 0x81, 0xF8,
    0x3C, 0x07, 0xE0, 0x00, 0x1F, 0x80, 0x01, 0xFE, 0x00, 0x3F, 0xF8, 0x03,
    0xE7, 0xE0, 0x3E, 0x1F, 0x83, 0xF0, 0x7E, 0x1F, 0x81, 0xF8, 0x7E, 0x07,
    0xE3, 0xF8, 0x1F, 0x8F, 0xE0, 0x7E, 0x3F, 0x83, 0xF8, 0xFF, 0x1F, 0xE1,
    0xFF, 0xDF, 0xF7, 0xFE, 0x3F, 0x07, 0xE0, 0xF8, 0xFF, 0x80, 0x00, 0x1F,
    0xC0, 0x00, 0x07, 0xE0, 0x00, 0x03, 0xF0, 0x00, 0x01, 0xF8, 0x00, 0x00,
    0xFC, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x1F, 0x80, 0x00,
    0x0F, 0xC7, 0xF0, 0x07, 0xEF, 0xFE, 0x03, 0xFC, 0x3F, 0x81, 0xFC, 0x0F,
    0xE0, 0xFC, 0x03, 0xF0, 0x7E, 0x01, 0xFC, 0x3F, 0x00, 0xFE, 0x1F, 0x80,
    0x3F, 0x8F, 0xC0, 0x1F, 0xC7, 0xE0, 0x0F, 0xE3, 0xF0, 0x07, 0xF1, 0xF8,
    0x03, 0xF8, 0xFC, 0x01, 0xFC, 0x7E, 0x00, 0xFE, 0x3F, 0x00, 0x7F, 0x1F,
    0x80, 0x3F, 0x0F, 0xC0, 0x1F, 0x87, 0xE0, 0x1F, 0xC3, 0xF0, 0x0F, 0xC1,
    0xF8, 0x07, 0xE0, 0xFE, 0x07, 0xE0, 0x73, 0x87, 0xE0, 0x30, 0xFF, 0xC0,
    0x10, 0x1F, 0x80, 0x00, 0x00, 0xFC, 0x00, 0x7F, 0xE0, 0x3E, 0x3E, 0x0F,
    0x83, 0xE3, 0xE0, 0x7C, 0x7C, 0x0F, 0x9F, 0x01, 0xF3, 0xE0, 0x1C, 0x7C,
    0x00, 0x1F, 0x80, 0x03, 0xF0, 0x00, 0x7E, 0x00, 0x0F, 0xC0, 0x01, 0xF8,
    0x00, 0x3F, 0x00, 0x07, 0xF0, 0x00, 0xFE, 0x00, 0x0F, 0xE0, 0x01, 0xFC,
    0x00, 0x1F, 0xC0, 0x21, 0xFE, 0x0C, 0x3F, 0xFF, 0x01, 0xFF, 0x80, 0x0F,
    0xC0, 0x00, 0x1F, 0xF8, 0x00, 0x03, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01,
    0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01,
    0xF8, 0x00, 0x01, 0xF8, 0x03, 0xF1, 0xF8, 0x07, 0xFD, 0xF8, 0x1F, 0xC7,
    0xF8, 0x1F, 0x83, 0xF8, 0x3F, 0x01, 0xF8, 0x7F, 0x01, 0xF8, 0x7E, 0x01,
    0xF8, 0x7E, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01,
    0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01,
    0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0x7E, 0x01, 0xF8, 0x7F, 0x01,
    0xF8, 0x3F, 0x03, 0xF8, 0x3F, 0x03, 0xF8, 0x1F, 0x87, 0xFC, 0x0F, 0xFD,
    0xFF, 0x03, 0xF1, 0xC0, 0x03, 0xF0, 0x03, 0xFF, 0x01, 0xE1, 0xE0, 0xF8,
    0x7C, 0x3C, 0x0F, 0x1F, 0x03, 0xE7, 0xC0, 0xFB, 0xF0, 0x3E, 0xFC, 0x0F,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0xFC, 0x00, 0x3F, 0x00, 0x0F,
    0xC0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x1F, 0x80, 0x07, 0xF0, 0x0C, 0xFC,
    0x06, 0x3F, 0xC3, 0x07, 0xFF, 0x80, 0xFF, 0xC0, 0x0F, 0xC0, 0x00, 0xFC,
    0x01, 0xFF, 0x81, 0xF1, 0xC1, 0xF0, 0xF0, 0xF8, 0xF8, 0xFC, 0x7C, 0x7E,
    0x1C, 0x3F, 0x00, 0x1F, 0x80, 0x0F, 0xC0, 0x07, 0xE0, 0x1F, 0xFF, 0x0F,
    0xFF, 0x80, 0xFC, 0x00, 0x7E, 0x00, 0x3F, 0x00, 0x1F, 0x80, 0x0F, 0xC0,
    0x07, 0xE0, 0x03, 0xF0, 0x01, 0xF8, 0x00, 0xFC, 0x00, 0x7E, 0x00, 0x3F,
    0x00, 0x1F, 0x80, 0x0F, 0xC0, 0x07, 0xE0, 0x03, 0xF0, 0x01, 0xF8, 0x00,
    0xFC, 0x00, 0x7E, 0x00, 0x7F, 0x80, 0xFF, 0xF8, 0x00, 0x07, 0xF0, 0x03,
    0xFF, 0xFC, 0xF8, 0x7F, 0xBE, 0x07, 0x87, 0xC0, 0xF9, 0xF8, 0x1F, 0xBF,
    0x03, 0xF7, 0xE0, 0x7E, 0xFC, 0x0F, 0xDF, 0x81, 0xF9, 0xF0, 0x3F, 0x3E,
    0x07, 0xC3, 0xE1, 0xF8, 0x3C, 0x7E, 0x01, 0xFF, 0x00, 0x60, 0x00, 0x38,
    0x00, 0x0F, 0x00, 0x01, 0xF0, 0x00, 0x7F, 0xFF, 0x0F, 0xFF, 0xF9, 0xFF,
    0xFF, 0x9F, 0xFF, 0xF9, 0xFF, 0xFF, 0x0F, 0xFF, 0xEF, 0x00, 0x3F, 0xC0,
    0x03, 0xF8, 0x00, 0x7F, 0x00, 0x1C, 0xF8, 0x07, 0x0F, 0xFF, 0xC0, 0x7F,
    0xC0, 0xFF, 0x80, 0x00, 0x3F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80,
    0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80,
    0x00, 0x1F, 0x80, 0x00, 0x1F, 0x87, 0xE0, 0x1F, 0x9F, 0xF0, 0x1F, 0xBF,
    0xF8, 0x1F, 0xF1, 0xF8, 0x1F, 0xC0, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80,
    0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80,
    0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80,
    0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80,
    0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x3F, 0xC1, 0xFE, 0xFF, 0xE3,
    0xFF, 0x0F, 0x07, 0xE1, 0xFE, 0x3F, 0xC7, 0xF8, 0x7F, 0x03, 0xC0, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x7F, 0xC3, 0xF8, 0x3F, 0x07, 0xE0, 0xFC, 0x1F,
    0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x07, 0xE0, 0xFC, 0x1F, 0x83,
    0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x07, 0xE1, 0xFE, 0xFF, 0xE0, 0x00,
    0x70, 0x07, 0xF0, 0x3F, 0xC0, 0xFF, 0x03, 0xFC, 0x07, 0xF0, 0x0F, 0x80,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x01, 0xFC, 0x03, 0xF0,
    0x0F, 0xC0, 0x3F, 0x00, 0xFC, 0x03, 0xF0, 0x0F, 0xC0, 0x3F, 0x00, 0xFC,
    0x03, 0xF0, 0x0F, 0xC0, 0x3F, 0x00, 0xFC, 0x03, 0xF0, 0x0F, 0xC0, 0x3F,
    0x00, 0xFC, 0x03, 0xF0, 0x0F, 0xC0, 0x3F, 0x00, 0xFC, 0x03, 0xF0, 0x0F,
    0xDC, 0x3F, 0xF8, 0xFB, 0xE3, 0xEF, 0x0F, 0xBC, 0x7C, 0x7F, 0xE0, 0x7E,
    0x00, 0xFF, 0x80, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xE0, 0x00, 0x03, 0xF0,
    0x00, 0x01, 0xF8, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3F,
    0x00, 0x00, 0x1F, 0x80, 0x00, 0x0F, 0xC0, 0x00, 0x07, 0xE1, 0xFF, 0x83,
    0xF0, 0x3F, 0x01, 0xF8, 0x0E, 0x00, 0xFC, 0x06, 0x00, 0x7E, 0x06, 0x00,
    0x3F, 0x06, 0x00, 0x1F, 0x86, 0x00, 0x0F, 0xC7, 0x00, 0x07, 0xE7, 0x80,
    0x03, 0xF7, 0xE0, 0x01, 0xFF, 0xF8, 0x00, 0xFF, 0xFC, 0x00, 0x7E, 0x7F,
    0x00, 0x3F, 0x1F, 0xC0, 0x1F, 0x8F, 0xE0, 0x0F, 0xC3, 0xF8, 0x07, 0xE0,
    0xFE, 0x03, 0xF0, 0x7F, 0x81, 0xF8, 0x1F, 0xC0, 0xFC, 0x0F, 0xF0, 0xFF,
    0x07, 0xFD, 0xFF, 0xC7, 0xFF, 0xFF, 0x87, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8,
    0x3F, 0x07, 0xE0, 0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F,
    0x07, 0xE0, 0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x07,
    0xE0, 0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x0F, 0xF7,
    0xFF, 0x00, 0x07, 0xE0, 0x3F, 0x07, 0xFC, 0xFF, 0x87, 0xFC, 0x0F, 0xEF,
    0xFE, 0x7F, 0xF0, 0x3F, 0xC3, 0xFF, 0x1F, 0x81, 0xFC, 0x0F, 0xE0, 0x7E,
    0x0F, 0xC0, 0x7E, 0x03, 0xF0, 0x7E, 0x03, 0xF0, 0x1F, 0x83, 0xF0, 0x1F,
    0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x07, 0xE0, 0xFC, 0x07, 0xE0, 0x3F, 0x07,
    0xE0, 0x3F, 0x01, 0xF8, 0x3F, 0x01, 0xF8, 0x0F, 0xC1, 0xF8, 0x0F, 0xC0,
    0x7E, 0x0F, 0xC0, 0x7E, 0x03, 0xF0, 0x7E, 0x03, 0xF0, 0x1F, 0x83, 0xF0,
    0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x07, 0xE0, 0xFC, 0x07, 0xE0, 0x3F,
    0x07, 0xE0, 0x3F, 0x01, 0xF8, 0x3F, 0x01, 0xF8, 0x0F, 0xC1, 0xF8, 0x0F,
    0xC0, 0x7E, 0x1F, 0xE0, 0xFF, 0x07, 0xFB, 0xFF, 0x8F, 0xFC, 0x7F, 0xE0,
    0x00, 0x07, 0xE0, 0xFF, 0x9F, 0xF0, 0x3F, 0xBF, 0xF8, 0x1F, 0xF1, 0xF8,
    0x1F, 0xC0, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC,
    0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC,
    0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC,
    0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC, 0x1F, 0x80, 0xFC,
    0x1F, 0x80, 0xFC, 0x3F, 0xC1, 0xFE, 0xFF, 0xE3, 0xFF, 0x01, 0xFC, 0x00,
    0x3F, 0xF8, 0x03, 0xE3, 0xE0, 0x3E, 0x0F, 0x83, 0xF0, 0x7E, 0x1F, 0x01,
    0xF1, 0xF8, 0x0F, 0xCF, 0xC0, 0x7E, 0xFE, 0x03, 0xFF, 0xF0, 0x1F, 0xFF,
    0x80, 0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0x3F, 0xFF, 0x01, 0xFF, 0xF8, 0x0F,
    0xFF, 0xC0, 0x7F, 0x7E, 0x03, 0xF3, 0xF0, 0x1F, 0x8F, 0x80, 0xF8, 0x7E,
    0x0F, 0xC1, 0xF0, 0x7C, 0x07, 0xC7, 0xC0, 0x1F, 0xFC, 0x00, 0x3F, 0x80,
    0x00, 0x0F, 0xC0, 0xFF, 0xBF, 0xF0, 0x3F, 0xF1, 0xF8, 0x1F, 0xC0, 0xFC,
    0x1F, 0xC0, 0xFC, 0x1F, 0x80, 0xFE, 0x1F, 0x80, 0x7E, 0x1F, 0x80, 0x7F,
    0x1F, 0x80, 0x7F, 0x1F, 0x80, 0x7F, 0x1F, 0x80, 0x7F, 0x1F, 0x80, 0x7F,
    0x1F, 0x80, 0x7F, 0x1F, 0x80, 0x7F, 0x1F, 0x80, 0x7F, 0x1F, 0x80, 0x7F,
    0x1F, 0x80, 0x7E, 0x1F, 0x80, 0x7E, 0x1F, 0x80, 0xFE, 0x1F, 0x80, 0xFC,
    0x1F, 0xC1, 0xF8, 0x1F, 0xE3, 0xF8, 0x1F, 0xBF, 0xE0, 0x1F, 0x8F, 0xC0,
    0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00,
    0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x3F, 0xC0, 0x00, 0xFF, 0xF8, 0x00,
    0x00, 0xF8, 0x08, 0x07, 0xFE, 0x18, 0x0F, 0xC7, 0x38, 0x1F, 0x83, 0xF8,
    0x3F, 0x01, 0xF8, 0x3F, 0x01, 0xF8, 0x7F, 0x01, 0xF8, 0x7E, 0x01, 0xF8,
    0x7E, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8,
    0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8, 0xFE, 0x01, 0xF8,
    0xFE, 0x01, 0xF8, 0x7E, 0x01, 0xF8, 0x7F, 0x01, 0xF8, 0x7F, 0x01, 0xF8,
    0x3F, 0x83, 0xF8, 0x1F, 0xC7, 0xF8, 0x0F, 0xFD, 0xF8, 0x03, 0xF1, 0xF8,
    0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8,
    0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x03, 0xFC, 0x00, 0x0F, 0xFF,
    0x00, 0x07, 0x9F, 0xF3, 0xF8, 0xFE, 0xFF, 0x8F, 0xFF, 0xF1, 0xFE, 0x7E,
    0x3F, 0x87, 0x87, 0xE0, 0x00, 0xFC, 0x00, 0x1F, 0x80, 0x03, 0xF0, 0x00,
    0x7E, 0x00, 0x0F, 0xC0, 0x01, 0xF8, 0x00, 0x3F, 0x00, 0x07, 0xE0, 0x00,
    0xFC, 0x00, 0x1F, 0x80, 0x03, 0xF0, 0x00, 0x7E, 0x00, 0x0F, 0xC0, 0x01,
    0xF8, 0x00, 0x7F, 0x80, 0x3F, 0xFC, 0x00, 0x0F, 0x84, 0x3F, 0xF8, 0xE1,
    0xF3, 0x80, 0xEF, 0x00, 0xDE, 0x01, 0xBE, 0x01, 0x7E, 0x00, 0xFF, 0x01,
    0xFF, 0x81, 0xFF, 0xC3, 0xFF, 0xC3, 0xFF, 0xC1, 0xFF, 0x80, 0xFF, 0x80,
    0x7F, 0x80, 0x7F, 0x80, 0x7F, 0x00, 0x7E, 0x00, 0xFE, 0x01, 0xDF, 0x0F,
    0x37, 0xFC, 0x43, 0xF0, 0x01, 0x00, 0x0C, 0x00, 0x70, 0x01, 0xC0, 0x0F,
    0x00, 0x7C, 0x03, 0xF0, 0x1F, 0xC0, 0xFF, 0xF3, 0xFF, 0xC3, 0xF0, 0x0F,
    0xC0, 0x3F, 0x00, 0xFC, 0x03, 0xF0, 0x0F, 0xC0, 0x3F, 0x00, 0xFC, 0x03,
    0xF0, 0x0F, 0xC0, 0x3F, 0x00, 0xFC, 0x03, 0xF0, 0x0F, 0xC0, 0x3F, 0x00,
    0xFC, 0x23, 0xF0, 0x8F, 0xE6, 0x1F, 0xF0, 0x7F, 0x80, 0xF8, 0x00, 0xFF,
    0x87, 0xFC, 0x1F, 0xC0, 0xFE, 0x07, 0xE0, 0x3F, 0x03, 0xF0, 0x1F, 0x81,
    0xF8, 0x0F, 0xC0, 0xFC, 0x07, 0xE0, 0x7E, 0x03, 0xF0, 0x3F, 0x01, 0xF8,
    0x1F, 0x80, 0xFC, 0x0F, 0xC0, 0x7E, 0x07, 0xE0, 0x3F, 0x03, 0xF0, 0x1F,
    0x81, 0xF8, 0x0F, 0xC0, 0xFC, 0x07, 0xE0, 0x7E, 0x03, 0xF0, 0x3F, 0x01,
    0xF8, 0x1F, 0x80, 0xFC, 0x0F, 0xC0, 0x7E, 0x07, 0xE0, 0x7F, 0x03, 0xF8,
    0x7F, 0xC0, 0xFF, 0xEF, 0xF8, 0x3F, 0xE7, 0xC0, 0x0F, 0xC2, 0x00, 0xFF,
    0xF1, 0xFC, 0xFF, 0x01, 0xE3, 0xFC, 0x03, 0x07, 0xF0, 0x0C, 0x1F, 0xC0,
    0x60, 0x3F, 0x81, 0x80, 0xFE, 0x04, 0x01, 0xF8, 0x30, 0x07, 0xF0, 0xC0,
    0x1F, 0xC6, 0x00, 0x3F, 0x98, 0x00, 0xFE, 0x40, 0x01, 0xFB, 0x00, 0x07,
    0xFC, 0x00, 0x1F, 0xE0, 0x00, 0x3F, 0x80, 0x00, 0xFE, 0x00, 0x01, 0xF0,
    0x00, 0x07, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0x38, 0x00, 0x00, 0xE0, 0x00,
    0x01, 0x00, 0x00, 0xFF, 0xE7, 0xFF, 0x3F, 0xBF, 0xE0, 0xFE, 0x07, 0x0F,
    0xE0, 0x7F, 0x03, 0x83, 0xF0, 0x1F, 0x81, 0x81, 0xFC, 0x0F, 0xC0, 0xC0,
    0xFE, 0x07, 0xF0, 0x40, 0x3F, 0x03, 0xF8, 0x60, 0x1F, 0xC3, 0xFC, 0x30,
    0x07, 0xE1, 0xFE, 0x10, 0x03, 0xF0, 0x9F, 0x98, 0x01, 0xFC, 0xCF, 0xCC,
    0x00, 0x7E, 0x67, 0xEC, 0x00, 0x3F, 0xE1, 0xFE, 0x00, 0x1F, 0xF0, 0xFE,
    0x00, 0x07, 0xF0, 0x7F, 0x00, 0x03, 0xF8, 0x3F, 0x80, 0x00, 0xFC, 0x0F,
    0x80, 0x00, 0x7C, 0x07, 0xC0, 0x00, 0x3E, 0x03, 0xE0, 0x00, 0x0F, 0x00,
    0xE0, 0x00, 0x07, 0x00, 0x70, 0x00, 0x03, 0x80, 0x38, 0x00, 0x00, 0x80,
    0x08, 0x00, 0xFF, 0xF3, 0xFD, 0xFF, 0x03, 0xC3, 0xFC, 0x0E, 0x07, 0xF0,
    0x30, 0x1F, 0xE1, 0x80, 0x3F, 0x8C, 0x00, 0x7F, 0x70, 0x01, 0xFF, 0x80,
    0x03, 0xFC, 0x00, 0x0F, 0xF0, 0x00, 0x1F, 0xE0, 0x00, 0x3F, 0x80, 0x00,
    0xFF, 0x00, 0x07, 0xFE, 0x00, 0x1B, 0xF8, 0x00, 0xCF, 0xF0, 0x06, 0x1F,
    0xC0, 0x38, 0x3F, 0x80, 0xC0, 0xFF, 0x07, 0x01, 0xFC, 0x3C, 0x07, 0xFB,
    0xFC, 0x7F, 0xF0, 0xFF, 0xE3, 0xFB, 0xFC, 0x07, 0x8F, 0xE0, 0x18, 0x7F,
    0x01, 0x81, 0xF8, 0x0C, 0x0F, 0xE0, 0x60, 0x7F, 0x06, 0x01, 0xF8, 0x30,
    0x0F, 0xE1, 0x80, 0x7F, 0x18, 0x01, 0xF8, 0xC0, 0x0F, 0xE6, 0x00, 0x3F,
    0x60, 0x01, 0xFF, 0x00, 0x0F, 0xF0, 0x00, 0x3F, 0x80, 0x01, 0xFC, 0x00,
    0x07, 0xC0, 0x00, 0x3E, 0x00, 0x01, 0xF0, 0x00, 0x07, 0x00, 0x00, 0x38,
    0x00, 0x00, 0x80, 0x00, 0x0C, 0x00, 0x00, 0x60, 0x03, 0x82, 0x00, 0x3E,
    0x30, 0x01, 0xF1, 0x00, 0x0F, 0x98, 0x00, 0x3F, 0x80, 0x00, 0xF0, 0x00,
    0x00, 0x7F, 0xFF, 0xEF, 0xFF, 0xFD, 0xE0, 0x7F, 0x30, 0x1F, 0xC6, 0x07,
    0xF8, 0x80, 0xFE, 0x00, 0x3F, 0xC0, 0x07, 0xF0, 0x01, 0xFC, 0x00, 0x3F,
    0x80, 0x0F, 0xE0, 0x03, 0xFC, 0x00, 0x7F, 0x00, 0x1F, 0xE0, 0x03, 0xF8,
    0x00, 0xFE, 0x03, 0x3F, 0xC0, 0x67, 0xF0, 0x19, 0xFE, 0x07, 0x3F, 0x83,
    0xEF, 0xFF, 0xFD, 0xFF, 0xFF, 0x80, 0x00, 0x7C, 0x07, 0xE0, 0x3E, 0x00,
    0xF8, 0x07, 0xC0, 0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00,
    0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0,
    0x1F, 0x00, 0xF8, 0x03, 0xC0, 0x3C, 0x01, 0xF0, 0x00, 0xF0, 0x03, 0xE0,
    0x07, 0xC0, 0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x7C,
    0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F,
    0x00, 0x3E, 0x00, 0xF8, 0x01, 0xF8, 0x01, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xF0, 0xF8, 0x01, 0xF8, 0x01, 0xF0, 0x07, 0xC0, 0x0F, 0x80, 0x3E, 0x00,
    0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80,
    0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x00, 0x7C, 0x00, 0xF0,
    0x00, 0xF0, 0x03, 0xE0, 0x3C, 0x01, 0xF0, 0x0F, 0x80, 0x3E, 0x00, 0xF8,
    0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x3E,
    0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x01, 0xF0, 0x07, 0xC0, 0x7E,
    0x03, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0xFF, 0xC0, 0x47, 0xFF, 0xC3, 0x9F,
    0xFF, 0xFF, 0x70, 0x7F, 0xF8, 0x80, 0x7F, 0xC0, 0x00, 0x3E, 0x00};

const GFXglyph FreeSerifBold24pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 12, 0, 1},         // 0x20 ' '
    {0, 8, 34, 16, 4, -32},      // 0x21 '!'
    {34, 17, 13, 26, 4, -32},    // 0x22 '"'
    {62, 23, 33, 23, 0, -32},    // 0x23 '#'
    {157, 21, 39, 24, 1, -34},   // 0x24 '$'
    {260, 35, 34, 47, 6, -32},   // 0x25 '%'
    {409, 34, 34, 39, 3, -32},   // 0x26 '&'
    {554, 5, 13, 13, 4, -32},    // 0x27 '''
    {563, 12, 41, 16, 2, -32},   // 0x28 '('
    {625, 12, 41, 16, 1, -32},   // 0x29 ')'
    {687, 18, 21, 24, 3, -32},   // 0x2A '*'
    {735, 26, 25, 32, 3, -24},   // 0x2B '+'
    {817, 8, 15, 12, 2, -6},     // 0x2C ','
    {832, 11, 5, 16, 2, -12},    // 0x2D '-'
    {839, 8, 8, 12, 2, -6},      // 0x2E '.'
    {847, 15, 33, 13, -1, -32},  // 0x2F '/'
    {909, 22, 34, 23, 1, -32},   // 0x30 '0'
    {1003, 18, 33, 23, 3, -32},  // 0x31 '1'
    {1078, 21, 33, 24, 1, -32},  // 0x32 '2'
    {1165, 21, 34, 24, 1, -32},  // 0x33 '3'
    {1255, 21, 33, 24, 1, -32},  // 0x34 '4'
    {1342, 20, 32, 23, 2, -31},  // 0x35 '5'
    {1422, 21, 34, 24, 1, -32},  // 0x36 '6'
    {1512, 21, 32, 23, 1, -31},  // 0x37 '7'
    {1596, 21, 34, 23, 1, -32},  // 0x38 '8'
    {1686, 22, 34, 23, 1, -32},  // 0x39 '9'
    {1780, 8, 24, 16, 4, -22},   // 0x3A ':'
    {1804, 9, 31, 16, 3, -22},   // 0x3B ';'
    {1839, 26, 26, 32, 3, -24},  // 0x3C '<'
    {1924, 26, 17, 32, 3, -20},  // 0x3D '='
    {1980, 26, 26, 32, 3, -24},  // 0x3E '>'
    {2065, 18, 34, 24, 3, -32},  // 0x3F '?'
    {2142, 33, 34, 44, 5, -32},  // 0x40 '@'
    {2283, 32, 33, 34, 1, -32},  // 0x41 'A'
    {2415, 28, 32, 31, 1, -31},  // 0x42 'B'
    {2527, 30, 34, 33, 2, -32},  // 0x43 'C'
    {2655, 32, 32, 34, 1, -31},  // 0x44 'D'
    {2783, 28, 32, 32, 2, -31},  // 0x45 'E'
    {2895, 25, 32, 29, 2, -31},  // 0x46 'F'
    {2995, 33, 34, 36, 2, -32},  // 0x47 'G'
    {3136, 33, 32, 37, 2, -31},  // 0x48 'H'
    {3268, 15, 32, 18, 2, -31},  // 0x49 'I'
    {3328, 22, 37, 24, 0, -31},  // 0x4A 'J'
    {3430, 34, 32, 36, 2, -31},  // 0x4B 'K'
    {3566, 28, 32, 31, 2, -31},  // 0x4C 'L'
    {3678, 43, 32, 45, 0, -31},  // 0x4D 'M'
    {3850, 31, 32, 34, 1, -31},  // 0x4E 'N'
    {3974, 33, 34, 37, 2, -32},  // 0x4F 'O'
    {4115, 26, 32, 30, 2, -31},  // 0x50 'P'
    {4219, 33, 41, 37, 2, -32},  // 0x51 'Q'
    {4389, 31, 32, 34, 2, -31},  // 0x52 'R'
    {4513, 21, 34, 27, 3, -32},  // 0x53 'S'
    {4603, 28, 32, 30, 1, -31},  // 0x54 'T'
    {4715, 30, 33, 34, 2, -31},  // 0x55 'U'
    {4839, 33, 32, 33, 0, -31},  // 0x56 'V'
    {4971, 45, 33, 46, 1, -31},  // 0x57 'W'
    {5157, 32, 32, 34, 1, -31},  // 0x58 'X'
    {5285, 32, 32, 33, 1, -31},  // 0x59 'Y'
    {5413, 28, 32, 30, 1, -31},  // 0x5A 'Z'
    {5525, 11, 39, 16, 3, -31},  // 0x5B '['
    {5579, 15, 33, 13, -1, -32}, // 0x5C '\'
    {5641, 11, 39, 16, 2, -31},  // 0x5D ']'
    {5695, 21, 17, 27, 3, -31},  // 0x5E '^'
    {5740, 24, 3, 23, 0, 5},     // 0x5F '_'
    {5749, 11, 9, 16, 0, -33},   // 0x60 '`'
    {5762, 22, 24, 23, 1, -22},  // 0x61 'a'
    {5828, 25, 33, 26, 0, -31},  // 0x62 'b'
    {5932, 19, 24, 20, 1, -22},  // 0x63 'c'
    {5989, 24, 33, 26, 1, -31},  // 0x64 'd'
    {6088, 18, 24, 21, 1, -22},  // 0x65 'e'
    {6142, 17, 33, 18, 1, -32},  // 0x66 'f'
    {6213, 19, 32, 24, 2, -22},  // 0x67 'g'
    {6289, 24, 32, 26, 0, -31},  // 0x68 'h'
    {6385, 11, 33, 14, 1, -32},  // 0x69 'i'
    {6431, 14, 42, 18, 0, -32},  // 0x6A 'j'
    {6505, 25, 32, 26, 0, -31},  // 0x6B 'k'
    {6605, 11, 32, 13, 0, -31},  // 0x6C 'l'
    {6649, 37, 23, 39, 0, -22},  // 0x6D 'm'
    {6756, 24, 23, 26, 0, -22},  // 0x6E 'n'
    {6825, 21, 24, 24, 1, -22},  // 0x6F 'o'
    {6888, 24, 32, 26, 0, -22},  // 0x70 'p'
    {6984, 24, 32, 26, 1, -22},  // 0x71 'q'
    {7080, 19, 23, 20, 0, -22},  // 0x72 'r'
    {7135, 15, 24, 19, 2, -22},  // 0x73 's'
    {7180, 14, 31, 16, 1, -29},  // 0x74 't'
    {7235, 25, 23, 27, 0, -21},  // 0x75 'u'
    {7307, 22, 23, 23, 0, -21},  // 0x76 'v'
    {7371, 33, 23, 33, 0, -21},  // 0x77 'w'
    {7466, 22, 22, 24, 1, -21},  // 0x78 'x'
    {7527, 21, 31, 23, 0, -21},  // 0x79 'y'
    {7609, 19, 22, 21, 1, -21},  // 0x7A 'z'
    {7662, 14, 42, 19, 1, -33},  // 0x7B '{'
    {7736, 4, 33, 10, 3, -32},   // 0x7C '|'
    {7753, 14, 42, 19, 4, -33},  // 0x7D '}'
    {7827, 22, 7, 24, 1, -14}};  // 0x7E '~'

const GFXfont FreeSerifBold24pt7b PROGMEM = {
    (uint8_t *)FreeSerifBold24pt7bBitmaps,
    (GFXglyph *)FreeSerifBold24pt7bGlyphs, 0x20, 0x7E, 56};

// Approx. 8519 bytes
