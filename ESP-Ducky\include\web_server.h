/**
 * ESP-Ducky Web Server
 * 
 * Handles web interface for payload management
 * Author: <PERSON> Raihan - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#ifndef WEB_SERVER_H
#define WEB_SERVER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include "config.h"

// Web server response types
enum ResponseType {
    RESPONSE_HTML,
    RESPONSE_JSON,
    RESPONSE_CSS,
    RESPONSE_JS,
    RESPONSE_ICO,
    RESPONSE_SVG,
    RESPONSE_TEXT
};

// Upload status
enum UploadStatus {
    UPLOAD_IDLE,
    UPLOAD_RECEIVING,
    UPLOAD_SUCCESS,
    UPLOAD_ERROR,
    UPLOAD_TOO_LARGE,
    UPLOAD_INVALID_FORMAT
};

class WebServerManager {
private:
    WebServer* server;
    bool serverActive;
    UploadStatus uploadStatus;
    String uploadMessage;
    size_t uploadSize;
    String uploadFilename;
    
    // Configuration
    String apSSID;
    String apPassword;
    String deviceName;
    
    // Private methods
    void setupRoutes();
    void handleRoot();
    void handleAPI();
    void handleUpload();
    void handleFileUpload();
    void handleSettings();
    void handleReboot();
    void handleNotFound();
    
    // API endpoints
    void apiGetPayloads();
    void apiDeletePayload();
    void apiGetSettings();
    void apiUpdateSettings();
    void apiGetStatus();
    void apiReboot();
    
    // File serving
    void serveFile(String path, ResponseType type);
    String getContentType(String filename);
    bool loadFromSPIFFS(String path, String& content);
    
    // Template processing
    String processTemplate(String content);
    String replaceTemplateVariables(String content);
    
    // Utility
    void sendJSONResponse(int code, String message, JsonDocument* data = nullptr);
    void sendErrorResponse(int code, String message);
    void logRequest(String method, String uri, int code);
    
public:
    WebServerManager();
    ~WebServerManager();
    
    // Initialization
    bool begin();
    void end();
    
    // Server control
    bool startAP();
    bool stopAP();
    bool isAPActive();
    bool isServerActive();
    
    // Configuration
    void setAPCredentials(String ssid, String password);
    void setDeviceName(String name);
    String getAPSSID() { return apSSID; }
    String getAPPassword() { return apPassword; }
    String getAPIP();
    
    // Update handling
    void handleClient();
    void update();
    
    // Status
    int getConnectedClients();
    String getServerStatus();
    UploadStatus getUploadStatus() { return uploadStatus; }
    String getUploadMessage() { return uploadMessage; }
    
    // Statistics
    unsigned long getUptime();
    size_t getTotalRequests();
    size_t getSuccessfulRequests();
    size_t getErrorRequests();
    
    // Utility
    void printServerInfo();
    void resetStatistics();
};

// Global web server manager instance
extern WebServerManager webServerManager;

#endif // WEB_SERVER_H
