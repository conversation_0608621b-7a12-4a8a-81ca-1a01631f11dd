/**
 * Blecky - DuckyScript Payload Executor for ESP32
 * Clean implementation using proven BleKeyboard technique
 * 
 * Author: <PERSON> <<EMAIL>>
 * Organization: SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 * Version: 1.0.0
 * License: MIT
 * 
 * Based on proven working technique from BLEHID-SD project
 * Uses T-vK ESP32-BLE-Keyboard library for maximum Windows compatibility
 */

#include "Blecky.h"
#include "BleKeyboard.h"

// Global BLE keyboard instance
BleKeyboard bleKeyboard;

// Helper function to press individual keys (based on BLEHID-SD Press function)
void pressKey(String key) {
    if (key.length() == 1) {
        char c = key[0];
        bleKeyboard.press(c);
    }
    else if (key.equals("ENTER") || key.equals("RETURN")) {
        bleKeyboard.press(KEY_RETURN);
    }
    else if (key.equals("CTRL") || key.equals("CONTROL")) {
        bleKeyboard.press(KEY_LEFT_CTRL);
    }
    else if (key.equals("SHIFT")) {
        bleKeyboard.press(KEY_LEFT_SHIFT);
    }
    else if (key.equals("ALT")) {
        bleKeyboard.press(KEY_LEFT_ALT);
    }
    else if (key.equals("GUI") || key.equals("WIN") || key.equals("WINDOWS") || key.equals("CMD")) {
        bleKeyboard.press(KEY_LEFT_GUI);
    }
    else if (key.equals("UP") || key.equals("UPARROW")) {
        bleKeyboard.press(KEY_UP_ARROW);
    }
    else if (key.equals("DOWN") || key.equals("DOWNARROW")) {
        bleKeyboard.press(KEY_DOWN_ARROW);
    }
    else if (key.equals("LEFT") || key.equals("LEFTARROW")) {
        bleKeyboard.press(KEY_LEFT_ARROW);
    }
    else if (key.equals("RIGHT") || key.equals("RIGHTARROW")) {
        bleKeyboard.press(KEY_RIGHT_ARROW);
    }
    else if (key.equals("DELETE") || key.equals("DEL")) {
        bleKeyboard.press(KEY_DELETE);
    }
    else if (key.equals("PAGEUP") || key.equals("PGUP")) {
        bleKeyboard.press(KEY_PAGE_UP);
    }
    else if (key.equals("PAGEDOWN") || key.equals("PGDN")) {
        bleKeyboard.press(KEY_PAGE_DOWN);
    }
    else if (key.equals("HOME")) {
        bleKeyboard.press(KEY_HOME);
    }
    else if (key.equals("ESC") || key.equals("ESCAPE")) {
        bleKeyboard.press(KEY_ESC);
    }
    else if (key.equals("INSERT") || key.equals("INS")) {
        bleKeyboard.press(KEY_INSERT);
    }
    else if (key.equals("TAB")) {
        bleKeyboard.press(KEY_TAB);
    }
    else if (key.equals("END")) {
        bleKeyboard.press(KEY_END);
    }
    else if (key.equals("BACKSPACE")) {
        bleKeyboard.press(KEY_BACKSPACE);
    }
    else if (key.equals("CAPSLOCK")) {
        bleKeyboard.press(KEY_CAPS_LOCK);
    }
    else if (key.equals("F1")) {
        bleKeyboard.press(KEY_F1);
    }
    else if (key.equals("F2")) {
        bleKeyboard.press(KEY_F2);
    }
    else if (key.equals("F3")) {
        bleKeyboard.press(KEY_F3);
    }
    else if (key.equals("F4")) {
        bleKeyboard.press(KEY_F4);
    }
    else if (key.equals("F5")) {
        bleKeyboard.press(KEY_F5);
    }
    else if (key.equals("F6")) {
        bleKeyboard.press(KEY_F6);
    }
    else if (key.equals("F7")) {
        bleKeyboard.press(KEY_F7);
    }
    else if (key.equals("F8")) {
        bleKeyboard.press(KEY_F8);
    }
    else if (key.equals("F9")) {
        bleKeyboard.press(KEY_F9);
    }
    else if (key.equals("F10")) {
        bleKeyboard.press(KEY_F10);
    }
    else if (key.equals("F11")) {
        bleKeyboard.press(KEY_F11);
    }
    else if (key.equals("F12")) {
        bleKeyboard.press(KEY_F12);
    }
    else if (key.equals("SPACE")) {
        bleKeyboard.press(' ');
    }
}

// New function to handle keys while preserving case for letters (CRITICAL FIX)
void pressKeyPreserveCase(String key) {
    // Handle modifiers (always uppercase)
    if (key.equalsIgnoreCase("CTRL") || key.equalsIgnoreCase("CONTROL")) {
        bleKeyboard.press(KEY_LEFT_CTRL);
    }
    else if (key.equalsIgnoreCase("SHIFT")) {
        bleKeyboard.press(KEY_LEFT_SHIFT);
    }
    else if (key.equalsIgnoreCase("ALT")) {
        bleKeyboard.press(KEY_LEFT_ALT);
    }
    else if (key.equalsIgnoreCase("GUI") || key.equalsIgnoreCase("WIN") ||
             key.equalsIgnoreCase("WINDOWS") || key.equalsIgnoreCase("CMD")) {
        bleKeyboard.press(KEY_LEFT_GUI);
    }
    // Handle special keys (case insensitive)
    else if (key.equalsIgnoreCase("ENTER") || key.equalsIgnoreCase("RETURN")) {
        bleKeyboard.press(KEY_RETURN);
    }
    else if (key.equalsIgnoreCase("ESC") || key.equalsIgnoreCase("ESCAPE")) {
        bleKeyboard.press(KEY_ESC);
    }
    else if (key.equalsIgnoreCase("TAB")) {
        bleKeyboard.press(KEY_TAB);
    }
    else if (key.equalsIgnoreCase("SPACE")) {
        bleKeyboard.press(' ');
    }
    else if (key.equalsIgnoreCase("BACKSPACE")) {
        bleKeyboard.press(KEY_BACKSPACE);
    }
    else if (key.equalsIgnoreCase("DELETE") || key.equalsIgnoreCase("DEL")) {
        bleKeyboard.press(KEY_DELETE);
    }
    else if (key.equalsIgnoreCase("INSERT") || key.equalsIgnoreCase("INS")) {
        bleKeyboard.press(KEY_INSERT);
    }
    else if (key.equalsIgnoreCase("HOME")) {
        bleKeyboard.press(KEY_HOME);
    }
    else if (key.equalsIgnoreCase("END")) {
        bleKeyboard.press(KEY_END);
    }
    else if (key.equalsIgnoreCase("PAGEUP") || key.equalsIgnoreCase("PGUP")) {
        bleKeyboard.press(KEY_PAGE_UP);
    }
    else if (key.equalsIgnoreCase("PAGEDOWN") || key.equalsIgnoreCase("PGDN")) {
        bleKeyboard.press(KEY_PAGE_DOWN);
    }
    else if (key.equalsIgnoreCase("UP") || key.equalsIgnoreCase("UPARROW")) {
        bleKeyboard.press(KEY_UP_ARROW);
    }
    else if (key.equalsIgnoreCase("DOWN") || key.equalsIgnoreCase("DOWNARROW")) {
        bleKeyboard.press(KEY_DOWN_ARROW);
    }
    else if (key.equalsIgnoreCase("LEFT") || key.equalsIgnoreCase("LEFTARROW")) {
        bleKeyboard.press(KEY_LEFT_ARROW);
    }
    else if (key.equalsIgnoreCase("RIGHT") || key.equalsIgnoreCase("RIGHTARROW")) {
        bleKeyboard.press(KEY_RIGHT_ARROW);
    }
    else if (key.equalsIgnoreCase("CAPSLOCK")) {
        bleKeyboard.press(KEY_CAPS_LOCK);
    }
    // Function keys F1-F12
    else if (key.equalsIgnoreCase("F1")) {
        bleKeyboard.press(KEY_F1);
    }
    else if (key.equalsIgnoreCase("F2")) {
        bleKeyboard.press(KEY_F2);
    }
    else if (key.equalsIgnoreCase("F3")) {
        bleKeyboard.press(KEY_F3);
    }
    else if (key.equalsIgnoreCase("F4")) {
        bleKeyboard.press(KEY_F4);
    }
    else if (key.equalsIgnoreCase("F5")) {
        bleKeyboard.press(KEY_F5);
    }
    else if (key.equalsIgnoreCase("F6")) {
        bleKeyboard.press(KEY_F6);
    }
    else if (key.equalsIgnoreCase("F7")) {
        bleKeyboard.press(KEY_F7);
    }
    else if (key.equalsIgnoreCase("F8")) {
        bleKeyboard.press(KEY_F8);
    }
    else if (key.equalsIgnoreCase("F9")) {
        bleKeyboard.press(KEY_F9);
    }
    else if (key.equalsIgnoreCase("F10")) {
        bleKeyboard.press(KEY_F10);
    }
    else if (key.equalsIgnoreCase("F11")) {
        bleKeyboard.press(KEY_F11);
    }
    else if (key.equalsIgnoreCase("F12")) {
        bleKeyboard.press(KEY_F12);
    }
    // Handle single characters (PRESERVE ORIGINAL CASE - THIS IS THE KEY FIX!)
    else if (key.length() == 1) {
        char c = key[0];
        bleKeyboard.press(c);  // This preserves case - 'r' stays 'r', 'R' stays 'R'
    }
}

// ============================================================================
// Main Blecky Class Implementation
// ============================================================================

Blecky::Blecky(const char* deviceName, const char* manufacturer, uint8_t battery) 
    : _backend(nullptr), _defaultDelay(50), _debug(false), 
      _deviceName(deviceName), _manufacturer(manufacturer), _battery(battery) {
    // No backend needed - using direct BleKeyboard approach
}

Blecky::~Blecky() {
    // No cleanup needed for BleKeyboard
}

void Blecky::begin(bool waitForConnection) {
    debugPrint("[Blecky] Initializing BLE keyboard...");
    
    // Set device name and manufacturer
    bleKeyboard.setName(_deviceName.c_str());
    bleKeyboard.begin();
    
    debugPrint("[Blecky] BLE keyboard initialized");
    
    if (waitForConnection) {
        debugPrint("[Blecky] Waiting for connection...");
        while (!bleKeyboard.isConnected()) {
            delay(100);
        }
        debugPrint("[Blecky] Connected to host");
    }
}

bool Blecky::isConnected() const {
    return bleKeyboard.isConnected();
}

void Blecky::setDebug(bool on) {
    _debug = on;
    if (_debug) {
        debugPrint("[Blecky] Debug logging enabled");
    }
}

bool Blecky::setPayloadFromString(const String &payload) {
    _payload = payload;
    debugPrint("[Blecky] Payload loaded from string (" + String(_payload.length()) + " chars)");
    return true;
}

bool Blecky::setPayloadFromSPIFFS(const char* path) {
    debugPrint("[Blecky] Loading payload from SPIFFS: " + String(path));
    
    if (!SPIFFS.begin(true)) {
        debugPrint("[Blecky] ERROR: Failed to mount SPIFFS");
        return false;
    }
    
    File file = SPIFFS.open(path, "r");
    if (!file) {
        debugPrint("[Blecky] ERROR: Failed to open file: " + String(path));
        return false;
    }
    
    _payload = file.readString();
    file.close();
    
    debugPrint("[Blecky] Payload loaded from SPIFFS (" + String(_payload.length()) + " chars)");
    return true;
}

bool Blecky::setPayloadFromSD(const char* path) {
    debugPrint("[Blecky] SD card support not implemented yet");
    return false;
}

bool Blecky::run() {
    if (_payload.length() == 0) {
        debugPrint("[Blecky] ERROR: No payload loaded");
        return false;
    }
    
    if (!isConnected()) {
        debugPrint("[Blecky] WARNING: Not connected to host");
    }
    
    debugPrint("[Blecky] Executing payload...");
    
    int lineStart = 0;
    int lineEnd = 0;
    
    while (lineEnd < _payload.length()) {
        lineEnd = _payload.indexOf('\n', lineStart);
        if (lineEnd == -1) {
            lineEnd = _payload.length();
        }
        
        String line = _payload.substring(lineStart, lineEnd);
        line.trim();
        
        if (line.length() > 0) {
            parseAndExecuteLine(line);
        }
        
        lineStart = lineEnd + 1;
    }
    
    debugPrint("[Blecky] Payload execution completed");
    return true;
}

void Blecky::executeCommand(const String &rawCmd) {
    parseAndExecuteLine(rawCmd);
}

void Blecky::setDefaultDelay(uint32_t ms) {
    _defaultDelay = ms;
    debugPrint("[Blecky] Default delay set to " + String(ms) + "ms");
}

void Blecky::debugPrint(const String &message) {
    if (_debug) {
        Serial.println(message);
    }
}

// DuckyScript parser implementation (based on BLEHID-SD Line function)
void Blecky::parseAndExecuteLine(const String &line) {
    String cmd = line;
    cmd.trim();

    if (cmd.length() == 0 || cmd.startsWith("REM") || cmd.startsWith("//")) {
        // Comment or empty line
        return;
    }

    debugPrint("[Blecky] Executing: " + line);

    // Store non-comment commands for REPEAT functionality
    String upperCmd = cmd;
    upperCmd.toUpperCase();
    if (!upperCmd.startsWith("REPEAT")) {
        _lastCommand = line;
    }

    int space_1 = cmd.indexOf(" ");

    if (space_1 == -1) {
        // Single word command
        String upperCmd = cmd;
        upperCmd.toUpperCase();
        pressKey(upperCmd);
    }
    else {
        String command = cmd.substring(0, space_1);
        command.toUpperCase();
        String argument = cmd.substring(space_1 + 1);

        if (command == "STRING" || command == "TYPE") {
            bleKeyboard.print(argument);
        }
        else if (command == "DELAY") {
            int delayTime = argument.toInt();
            debugPrint("[Blecky] Delaying " + String(delayTime) + "ms");
            delay(delayTime);
        }
        else if (command == "DEFAULT_DELAY") {
            int delayTime = argument.toInt();
            setDefaultDelay(delayTime);
        }
        else if (command == "PRESS") {
            // Handle key combinations like CTRL+ALT+DEL, GUI+r
            handleKeyCombo(argument);
        }
        else if (command == "REPEAT") {
            int count = argument.toInt();
            if (count > 0 && _lastCommand.length() > 0) {
                debugPrint("[Blecky] Repeating last command " + String(count) + " times");
                for (int i = 0; i < count; i++) {
                    parseAndExecuteLine(_lastCommand);
                }
            }
        }
        else {
            // Multi-word command - split by spaces and press each key
            String remain = cmd;
            while (remain.length() > 0) {
                int latest_space = remain.indexOf(" ");
                if (latest_space == -1) {
                    String upperKey = remain;
                    upperKey.toUpperCase();
                    pressKey(upperKey);
                    remain = "";
                } else {
                    String key = remain.substring(0, latest_space);
                    key.toUpperCase();
                    pressKey(key);
                    remain = remain.substring(latest_space + 1);
                }
                delay(5);
            }
        }
    }

    bleKeyboard.releaseAll();
    delay(10); // Fixed delay for better Windows compatibility
    delay(_defaultDelay);
}

// Handle key combinations like CTRL+ALT+DEL, GUI+r (Fixed to preserve case)
void Blecky::handleKeyCombo(const String &combo) {
    String workingCombo = combo;
    workingCombo.trim();

    // Special warning for dangerous combinations
    String upperCheck = combo;
    upperCheck.toUpperCase();
    if (upperCheck.indexOf("CTRL") != -1 && upperCheck.indexOf("ALT") != -1 && upperCheck.indexOf("DEL") != -1) {
        debugPrint("[Blecky] WARNING: Executing CTRL+ALT+DEL");
    }

    // Split by '+' and press each key (preserving original case for letters)
    String remain = workingCombo;
    while (remain.length() > 0) {
        int plus_pos = remain.indexOf("+");
        if (plus_pos == -1) {
            String key = remain;
            key.trim();
            pressKeyPreserveCase(key);
            remain = "";
        } else {
            String key = remain.substring(0, plus_pos);
            key.trim();
            pressKeyPreserveCase(key);
            remain = remain.substring(plus_pos + 1);
        }
        delay(8); // Increased delay for better Windows compatibility
    }
}
