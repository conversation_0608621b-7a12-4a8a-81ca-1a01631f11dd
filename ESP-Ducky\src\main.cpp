/**
 * ESP-<PERSON><PERSON> Simple Test Firmware
 * 
 * Basic test to ensure hardware is working
 * Author: <PERSON>han - SKR Electronics Lab
 */

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
#define SCREEN_ADDRESS 0x3C

Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println();
    Serial.println("==================================================");
    Serial.println("ESP-Ducky Simple Test Starting...");
    Serial.println("==================================================");
    
    // Initialize I2C
    Wire.begin(21, 22, 100000);  // SDA=21, SCL=22, 100kHz
    delay(200);
    
    // Scan for I2C devices
    Serial.println("Scanning I2C bus...");
    int deviceCount = 0;
    for (byte address = 1; address < 127; address++) {
        Wire.beginTransmission(address);
        byte error = Wire.endTransmission();
        if (error == 0) {
            Serial.printf("I2C device found at address 0x%02X\n", address);
            deviceCount++;
        }
        delay(1);
    }
    Serial.printf("Found %d I2C device(s)\n", deviceCount);
    
    // Initialize display
    Serial.print("Initializing display... ");
    if (display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
        Serial.println("OK");
        
        // Configure display for 0.96" with maximum visibility
        display.clearDisplay();
        display.setTextColor(SSD1306_WHITE);
        display.setTextSize(1);
        
        // Enhanced settings for 0.96" displays
        display.ssd1306_command(SSD1306_SETCONTRAST);
        display.ssd1306_command(0xFF); // Maximum contrast
        display.ssd1306_command(SSD1306_SETPRECHARGE);
        display.ssd1306_command(0xF1); // Brighter precharge
        display.ssd1306_command(SSD1306_SETVCOMDETECT);
        display.ssd1306_command(0x40); // Better contrast
        display.ssd1306_command(SSD1306_CHARGEPUMP);
        display.ssd1306_command(0x14); // Enable charge pump
        
        // Test display
        display.setCursor(0, 0);
        display.println("ESP-Ducky");
        display.println("Test Mode");
        display.println("Display OK!");
        display.display();
        
        Serial.println("Display test completed successfully");
    } else {
        Serial.println("FAILED");
        Serial.println("Display initialization failed");
    }
    
    Serial.println("==================================================");
    Serial.println("Simple test completed");
    Serial.println("Type 'test' to run display test");
    Serial.println("Type 'status' to show system status");
    Serial.println("==================================================");
}

void loop() {
    // Handle serial commands
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        command.toLowerCase();
        
        if (command == "test") {
            Serial.println("Running display test...");
            
            display.clearDisplay();
            display.setCursor(0, 0);
            display.setTextSize(1);
            display.println("Display Test");
            display.println("Line 2");
            display.println("Line 3");
            display.println("Line 4");
            display.setTextSize(2);
            display.println("BIG");
            display.display();
            
            Serial.println("Display test completed");
        }
        else if (command == "status") {
            Serial.println("=== System Status ===");
            Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
            Serial.printf("Chip Model: %s\n", ESP.getChipModel());
            Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
            Serial.printf("Flash Size: %d bytes\n", ESP.getFlashChipSize());
            Serial.println("=====================");
        }
        else {
            Serial.println("Unknown command. Available: test, status");
        }
    }
    
    delay(100);
}
