#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSansOblique18pt7bBitmaps[] PROGMEM = {
    0x03, 0x83, 0x81, 0xC0, 0xE0, 0x70, 0x78, 0x38, 0x1C, 0x0E, 0x07, 0x07,
    0x83, 0x81, 0xC0, 0xE0, 0x60, 0x30, 0x30, 0x18, 0x0C, 0x04, 0x00, 0x00,
    0x01, 0xC0, 0xE0, 0x70, 0x78, 0x00, 0x71, 0xDC, 0x7F, 0x3F, 0x8E, 0xE3,
    0xB8, 0xEC, 0x33, 0x0C, 0xC3, 0x00, 0x00, 0x38, 0x70, 0x01, 0xC3, 0x80,
    0x0C, 0x18, 0x00, 0xE1, 0xC0, 0x06, 0x0C, 0x00, 0x70, 0xE0, 0x03, 0x87,
    0x03, 0xFF, 0xFF, 0x1F, 0xFF, 0xF0, 0xFF, 0xFF, 0x80, 0x60, 0xC0, 0x07,
    0x0E, 0x00, 0x30, 0x60, 0x03, 0x87, 0x00, 0x18, 0x30, 0x1F, 0xFF, 0xF8,
    0xFF, 0xFF, 0xC7, 0xFF, 0xFC, 0x07, 0x0E, 0x00, 0x30, 0x70, 0x03, 0x87,
    0x00, 0x1C, 0x38, 0x00, 0xC1, 0x80, 0x0E, 0x1C, 0x00, 0x60, 0xC0, 0x00,
    0x00, 0x0C, 0x00, 0x07, 0xF8, 0x01, 0xFF, 0xC0, 0x3F, 0xFE, 0x07, 0x99,
    0xF0, 0xF1, 0x87, 0x0E, 0x18, 0x71, 0xC1, 0x87, 0x1C, 0x38, 0x01, 0xC3,
    0x00, 0x1C, 0x30, 0x01, 0xE3, 0x00, 0x0F, 0xB0, 0x00, 0xFF, 0x80, 0x03,
    0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x6F, 0xC0, 0x06, 0x3C, 0x00, 0xC1, 0xCE,
    0x0C, 0x1C, 0xE0, 0xC1, 0xCE, 0x0C, 0x38, 0xF1, 0xC3, 0x8F, 0x98, 0xF0,
    0x7F, 0xFE, 0x03, 0xFF, 0xC0, 0x0F, 0xF0, 0x00, 0x30, 0x00, 0x03, 0x00,
    0x00, 0x30, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x07, 0x03, 0xE0, 0x03,
    0x81, 0xFC, 0x00, 0xC0, 0xFF, 0x00, 0x60, 0x70, 0xE0, 0x38, 0x38, 0x18,
    0x1C, 0x0C, 0x06, 0x0E, 0x03, 0x01, 0x83, 0x00, 0xC0, 0xE1, 0x80, 0x38,
    0x70, 0xE0, 0x0F, 0xF8, 0x70, 0x01, 0xFC, 0x18, 0x00, 0x3E, 0x0C, 0x00,
    0x00, 0x06, 0x07, 0x80, 0x03, 0x87, 0xF8, 0x00, 0xC3, 0xFE, 0x00, 0x61,
    0xE1, 0xC0, 0x30, 0x60, 0x30, 0x1C, 0x30, 0x0C, 0x0E, 0x0C, 0x03, 0x03,
    0x03, 0x01, 0x81, 0x80, 0xE1, 0xE0, 0xC0, 0x1F, 0xF0, 0x70, 0x07, 0xF8,
    0x18, 0x00, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x07, 0xF8, 0x00, 0xFF, 0xC0,
    0x1E, 0x3C, 0x03, 0xC1, 0xC0, 0x38, 0x1C, 0x03, 0x81, 0xC0, 0x38, 0x38,
    0x03, 0xC7, 0x00, 0x1D, 0xE0, 0x01, 0xFC, 0x00, 0x1F, 0x00, 0x07, 0xF0,
    0x01, 0xF7, 0x87, 0x3C, 0x3C, 0xE7, 0x81, 0xCE, 0x70, 0x1F, 0xCE, 0x00,
    0xFC, 0xE0, 0x07, 0x8E, 0x00, 0x78, 0xF0, 0x1F, 0x8F, 0x87, 0xFC, 0x7F,
    0xF9, 0xC3, 0xFE, 0x1E, 0x1F, 0x80, 0xE0, 0x77, 0xFE, 0xEE, 0xCC, 0xC0,
    0x00, 0x30, 0x06, 0x00, 0xC0, 0x18, 0x03, 0x80, 0x30, 0x06, 0x00, 0xE0,
    0x0C, 0x01, 0xC0, 0x18, 0x03, 0x80, 0x38, 0x07, 0x00, 0x70, 0x07, 0x00,
    0x70, 0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x00,
    0xE0, 0x0E, 0x00, 0xE0, 0x06, 0x00, 0x70, 0x07, 0x00, 0x30, 0x03, 0x00,
    0x18, 0x00, 0x01, 0x80, 0x0C, 0x00, 0xC0, 0x0E, 0x00, 0xE0, 0x06, 0x00,
    0x70, 0x07, 0x00, 0x70, 0x07, 0x00, 0x70, 0x07, 0x00, 0x70, 0x07, 0x00,
    0x70, 0x07, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x01, 0xC0, 0x1C, 0x03,
    0x80, 0x38, 0x03, 0x00, 0x70, 0x06, 0x00, 0xC0, 0x1C, 0x01, 0x80, 0x30,
    0x06, 0x00, 0xC0, 0x00, 0x06, 0x01, 0x84, 0x47, 0xF7, 0xFF, 0xCF, 0xC1,
    0xE0, 0xD8, 0x67, 0x18, 0xC0, 0x00, 0x70, 0x00, 0x1C, 0x00, 0x0F, 0x00,
    0x03, 0x80, 0x00, 0xE0, 0x00, 0x38, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xC0, 0x70, 0x00, 0x1C, 0x00, 0x07, 0x00, 0x01, 0xC0, 0x00, 0xE0,
    0x00, 0x38, 0x00, 0x0E, 0x00, 0x3B, 0xDC, 0x21, 0x18, 0x98, 0xFF, 0xFF,
    0xFF, 0xE0, 0x7F, 0xFE, 0x00, 0x06, 0x00, 0x18, 0x00, 0x30, 0x00, 0xC0,
    0x01, 0x80, 0x06, 0x00, 0x0C, 0x00, 0x30, 0x00, 0x60, 0x01, 0x80, 0x03,
    0x00, 0x0C, 0x00, 0x18, 0x00, 0x60, 0x00, 0xC0, 0x03, 0x00, 0x06, 0x00,
    0x18, 0x00, 0x20, 0x00, 0xC0, 0x03, 0x00, 0x06, 0x00, 0x18, 0x00, 0x30,
    0x00, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x7C, 0x00, 0x7F, 0xC0, 0x7F, 0xF8,
    0x3E, 0x1E, 0x0F, 0x03, 0xC7, 0x80, 0x71, 0xC0, 0x1C, 0xE0, 0x07, 0x38,
    0x01, 0xDE, 0x00, 0x77, 0x00, 0x1D, 0xC0, 0x0F, 0x70, 0x03, 0xFC, 0x00,
    0xEE, 0x00, 0x3B, 0x80, 0x0E, 0xE0, 0x07, 0xB8, 0x01, 0xCE, 0x00, 0xF3,
    0x80, 0x38, 0xF0, 0x1E, 0x1E, 0x1F, 0x07, 0xFF, 0x80, 0xFF, 0xC0, 0x0F,
    0x80, 0x00, 0x00, 0xC0, 0x70, 0x3C, 0x3E, 0xFF, 0xBF, 0xEF, 0xF8, 0x1E,
    0x07, 0x01, 0xC0, 0x70, 0x1C, 0x0F, 0x03, 0x80, 0xE0, 0x38, 0x0E, 0x07,
    0x81, 0xC0, 0x70, 0x1C, 0x07, 0x01, 0xC0, 0xE0, 0x38, 0x00, 0x00, 0x3F,
    0x00, 0x0F, 0xFC, 0x03, 0xFF, 0xE0, 0x7C, 0x1E, 0x07, 0x80, 0xF0, 0xF0,
    0x07, 0x0E, 0x00, 0x70, 0xE0, 0x07, 0x00, 0x00, 0x70, 0x00, 0x0E, 0x00,
    0x01, 0xE0, 0x00, 0x3C, 0x00, 0x0F, 0x80, 0x03, 0xF0, 0x00, 0xFC, 0x00,
    0x1F, 0x00, 0x07, 0xC0, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x03, 0x80, 0x00,
    0x70, 0x00, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0x8F, 0xFF, 0xF0, 0xFF, 0xFF,
    0x00, 0x00, 0x7E, 0x00, 0x3F, 0xF0, 0x0F, 0xFF, 0x03, 0xC1, 0xF0, 0x70,
    0x0E, 0x1C, 0x01, 0xC3, 0x80, 0x38, 0xE0, 0x07, 0x00, 0x01, 0xC0, 0x00,
    0xF0, 0x03, 0xFC, 0x00, 0x7F, 0x00, 0x0F, 0xF0, 0x00, 0x1F, 0x00, 0x00,
    0xE0, 0x00, 0x1C, 0x00, 0x03, 0x9C, 0x00, 0x73, 0x80, 0x1E, 0x70, 0x03,
    0x8F, 0x00, 0xF1, 0xF0, 0x7C, 0x1F, 0xFF, 0x01, 0xFF, 0xC0, 0x0F, 0xC0,
    0x00, 0x00, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x78, 0x00, 0x3E, 0x00, 0x1F,
    0x80, 0x0F, 0xE0, 0x07, 0xF0, 0x03, 0xDC, 0x01, 0xE7, 0x00, 0x71, 0xC0,
    0x38, 0xF0, 0x1C, 0x38, 0x0E, 0x0E, 0x07, 0x03, 0x83, 0x80, 0xE1, 0xC0,
    0x70, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x70, 0x00, 0x38,
    0x00, 0x0E, 0x00, 0x03, 0x80, 0x00, 0xE0, 0x00, 0x38, 0x00, 0x01, 0xFF,
    0xF0, 0x3F, 0xFF, 0x03, 0xFF, 0xE0, 0x78, 0x00, 0x07, 0x00, 0x00, 0x70,
    0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0xFC, 0x01, 0xFF, 0xF0, 0x1F,
    0xFF, 0x83, 0xE0, 0x78, 0x3C, 0x03, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xC0,
    0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x18, 0x00, 0x03, 0x8E, 0x00, 0x78,
    0xE0, 0x0F, 0x0F, 0x81, 0xE0, 0x7F, 0xFC, 0x03, 0xFF, 0x80, 0x0F, 0xE0,
    0x00, 0x00, 0x7E, 0x00, 0x3F, 0xF0, 0x0F, 0xFF, 0x03, 0xE1, 0xF0, 0xF0,
    0x0E, 0x1C, 0x01, 0xC7, 0x00, 0x01, 0xE0, 0x00, 0x38, 0x00, 0x07, 0x1F,
    0x01, 0xCF, 0xF8, 0x3B, 0xFF, 0x87, 0xE0, 0xF8, 0xF0, 0x0F, 0x3C, 0x00,
    0xE7, 0x80, 0x1C, 0xE0, 0x03, 0x9C, 0x00, 0x73, 0x80, 0x1C, 0x70, 0x03,
    0x8F, 0x00, 0xE0, 0xF0, 0x78, 0x1F, 0xFF, 0x01, 0xFF, 0x80, 0x0F, 0xC0,
    0x00, 0x3F, 0xFF, 0xCF, 0xFF, 0xF7, 0xFF, 0xFC, 0x00, 0x0E, 0x00, 0x07,
    0x00, 0x03, 0x80, 0x00, 0xC0, 0x00, 0x70, 0x00, 0x38, 0x00, 0x1C, 0x00,
    0x0E, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x78, 0x00, 0x1C,
    0x00, 0x0E, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xF0, 0x00, 0x38, 0x00,
    0x1E, 0x00, 0x07, 0x00, 0x03, 0xC0, 0x00, 0xE0, 0x00, 0x00, 0x00, 0x7E,
    0x00, 0x3F, 0xF0, 0x1F, 0xFF, 0x07, 0xC1, 0xF0, 0xE0, 0x0E, 0x38, 0x01,
    0xC7, 0x00, 0x38, 0xE0, 0x0E, 0x1C, 0x01, 0xC3, 0xC0, 0xF0, 0x3F, 0xFC,
    0x03, 0xFE, 0x01, 0xFF, 0xF0, 0x7C, 0x1E, 0x1E, 0x01, 0xE3, 0x80, 0x1C,
    0xE0, 0x03, 0x9C, 0x00, 0x73, 0x80, 0x0E, 0x70, 0x03, 0x8F, 0x00, 0xF1,
    0xF0, 0x7C, 0x1F, 0xFF, 0x01, 0xFF, 0xC0, 0x0F, 0xC0, 0x00, 0x00, 0x7E,
    0x00, 0x3F, 0xF0, 0x1F, 0xFF, 0x07, 0xC1, 0xE0, 0xE0, 0x1E, 0x38, 0x01,
    0xC7, 0x00, 0x39, 0xC0, 0x07, 0x38, 0x00, 0xE7, 0x00, 0x3C, 0xE0, 0x07,
    0x9E, 0x01, 0xE3, 0xE0, 0xFC, 0x3F, 0xFB, 0x83, 0xFE, 0xF0, 0x3F, 0x1C,
    0x00, 0x03, 0x80, 0x00, 0xF0, 0x00, 0x1C, 0x70, 0x07, 0x8E, 0x01, 0xE1,
    0xE0, 0xF8, 0x1F, 0xFE, 0x01, 0xFF, 0x80, 0x0F, 0xC0, 0x00, 0x0E, 0x3C,
    0x78, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38,
    0xF1, 0xE3, 0x80, 0x07, 0x0F, 0x0F, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x78, 0x70, 0x10, 0x10,
    0x30, 0x20, 0xC0, 0x00, 0x00, 0x20, 0x00, 0x1C, 0x00, 0x1F, 0x80, 0x1F,
    0xC0, 0x0F, 0xC0, 0x0F, 0xE0, 0x07, 0xE0, 0x03, 0xF0, 0x00, 0xF0, 0x00,
    0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x3F, 0x00, 0x01, 0xF8,
    0x00, 0x0F, 0xC0, 0x00, 0x78, 0x00, 0x01, 0x00, 0x7F, 0xFF, 0xDF, 0xFF,
    0xF7, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xFB,
    0xFF, 0xFE, 0xFF, 0xFF, 0x80, 0x10, 0x00, 0x03, 0xC0, 0x00, 0x7E, 0x00,
    0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x3F,
    0x00, 0x01, 0xE0, 0x01, 0xF8, 0x00, 0xFC, 0x00, 0xFE, 0x00, 0x7E, 0x00,
    0x7F, 0x00, 0x3F, 0x00, 0x07, 0x00, 0x00, 0x80, 0x00, 0x00, 0x03, 0xF8,
    0x0F, 0xFC, 0x1F, 0xFE, 0x3C, 0x1F, 0x78, 0x07, 0x70, 0x07, 0xE0, 0x07,
    0xE0, 0x07, 0x00, 0x0E, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x78, 0x00, 0xF0,
    0x01, 0xC0, 0x03, 0x80, 0x07, 0x00, 0x0F, 0x00, 0x0E, 0x00, 0x0E, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00,
    0x3C, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x07,
    0xFF, 0xFE, 0x00, 0x0F, 0xE0, 0x3F, 0x80, 0x0F, 0x80, 0x03, 0xE0, 0x0F,
    0x00, 0x00, 0xF8, 0x0F, 0x00, 0x00, 0x3C, 0x0F, 0x01, 0xF0, 0x0F, 0x0F,
    0x03, 0xFD, 0xC7, 0x8F, 0x03, 0xFE, 0xE1, 0xC7, 0x03, 0xC3, 0x60, 0xE7,
    0x03, 0xC0, 0xF0, 0x77, 0x83, 0xC0, 0x70, 0x3B, 0x83, 0xC0, 0x78, 0x1D,
    0xC1, 0xC0, 0x38, 0x1F, 0xC1, 0xE0, 0x1C, 0x0E, 0xE0, 0xE0, 0x1C, 0x0F,
    0x70, 0x70, 0x0E, 0x07, 0x38, 0x38, 0x0E, 0x07, 0x9C, 0x1C, 0x0F, 0x07,
    0x8E, 0x0F, 0x0F, 0x8F, 0x87, 0x03, 0xFD, 0xFF, 0x83, 0xC1, 0xFC, 0xFF,
    0x80, 0xE0, 0x7C, 0x3F, 0x00, 0x78, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00,
    0x00, 0x07, 0x80, 0x00, 0x00, 0x01, 0xF8, 0x07, 0x00, 0x00, 0x7F, 0xFF,
    0x80, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x01,
    0xE0, 0x00, 0x07, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x80, 0x00, 0xFF,
    0x00, 0x01, 0xDE, 0x00, 0x07, 0x9C, 0x00, 0x0E, 0x38, 0x00, 0x3C, 0x70,
    0x00, 0x70, 0xF0, 0x01, 0xC1, 0xE0, 0x07, 0x83, 0xC0, 0x0E, 0x07, 0x80,
    0x38, 0x07, 0x00, 0x70, 0x0E, 0x01, 0xFF, 0xFC, 0x03, 0xFF, 0xFC, 0x0F,
    0xFF, 0xF8, 0x1C, 0x00, 0xF0, 0x70, 0x01, 0xE1, 0xE0, 0x01, 0xC3, 0x80,
    0x03, 0x8F, 0x00, 0x07, 0x1C, 0x00, 0x0E, 0x78, 0x00, 0x1E, 0xE0, 0x00,
    0x3C, 0x07, 0xFF, 0xC0, 0x3F, 0xFF, 0x81, 0xFF, 0xFC, 0x0E, 0x00, 0xF0,
    0xF0, 0x03, 0x87, 0x00, 0x1C, 0x38, 0x00, 0xE1, 0xC0, 0x07, 0x0E, 0x00,
    0x70, 0xF0, 0x03, 0x87, 0x00, 0x78, 0x3F, 0xFF, 0x81, 0xFF, 0xF8, 0x0F,
    0xFF, 0xF0, 0xE0, 0x03, 0xC7, 0x00, 0x0E, 0x38, 0x00, 0x71, 0xC0, 0x03,
    0x9E, 0x00, 0x1C, 0xE0, 0x00, 0xE7, 0x00, 0x0E, 0x38, 0x00, 0xF1, 0xC0,
    0x0F, 0x1F, 0xFF, 0xF0, 0xFF, 0xFF, 0x07, 0xFF, 0xE0, 0x00, 0x00, 0x1F,
    0x80, 0x03, 0xFF, 0x80, 0x1F, 0xFF, 0x01, 0xF8, 0x3E, 0x07, 0x80, 0x38,
    0x38, 0x00, 0xF1, 0xC0, 0x01, 0xCF, 0x00, 0x07, 0x38, 0x00, 0x01, 0xE0,
    0x00, 0x07, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x70, 0x00, 0x03, 0x80, 0x00,
    0x0E, 0x00, 0x00, 0x38, 0x00, 0x00, 0xE0, 0x00, 0x7B, 0x80, 0x01, 0xCE,
    0x00, 0x0F, 0x3C, 0x00, 0x38, 0x70, 0x01, 0xE1, 0xE0, 0x0F, 0x07, 0xC0,
    0xF8, 0x0F, 0xFF, 0xC0, 0x1F, 0xFC, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xFF,
    0xC0, 0x0F, 0xFF, 0xE0, 0x1F, 0xFF, 0xE0, 0x38, 0x03, 0xE0, 0xF0, 0x03,
    0xC1, 0xC0, 0x03, 0x83, 0x80, 0x03, 0x87, 0x00, 0x07, 0x1E, 0x00, 0x0E,
    0x3C, 0x00, 0x1C, 0x70, 0x00, 0x38, 0xE0, 0x00, 0x71, 0xC0, 0x00, 0xE7,
    0x80, 0x03, 0x8F, 0x00, 0x07, 0x1C, 0x00, 0x0E, 0x38, 0x00, 0x3C, 0x70,
    0x00, 0x71, 0xE0, 0x01, 0xE3, 0x80, 0x03, 0x87, 0x00, 0x0E, 0x0E, 0x00,
    0x3C, 0x1C, 0x01, 0xF0, 0x7F, 0xFF, 0xC0, 0xFF, 0xFE, 0x01, 0xFF, 0xF0,
    0x00, 0x07, 0xFF, 0xFE, 0x0F, 0xFF, 0xFC, 0x1F, 0xFF, 0xF0, 0x38, 0x00,
    0x00, 0xF0, 0x00, 0x01, 0xC0, 0x00, 0x03, 0x80, 0x00, 0x07, 0x00, 0x00,
    0x1E, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x70, 0x00, 0x00, 0xFF, 0xFF, 0x81,
    0xFF, 0xFF, 0x07, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x38,
    0x00, 0x00, 0x70, 0x00, 0x01, 0xE0, 0x00, 0x03, 0x80, 0x00, 0x07, 0x00,
    0x00, 0x0E, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x7F, 0xFF, 0xF0, 0xFF, 0xFF,
    0xC1, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFC, 0x1F, 0xFF, 0xF0, 0x7F, 0xFF,
    0xC1, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x38, 0x00, 0x00, 0xE0, 0x00, 0x03,
    0x80, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x01, 0xC0, 0x00, 0x07, 0xFF,
    0xF0, 0x1F, 0xFF, 0xC0, 0xFF, 0xFF, 0x03, 0x80, 0x00, 0x0E, 0x00, 0x00,
    0x38, 0x00, 0x00, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1C, 0x00, 0x00, 0x70,
    0x00, 0x01, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x3C, 0x00, 0x00, 0xE0, 0x00,
    0x03, 0x80, 0x00, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x7F, 0xF8, 0x01, 0xFF,
    0xFC, 0x03, 0xE0, 0x3E, 0x07, 0x80, 0x0E, 0x0F, 0x00, 0x0F, 0x1E, 0x00,
    0x07, 0x1C, 0x00, 0x07, 0x38, 0x00, 0x00, 0x38, 0x00, 0x00, 0x70, 0x00,
    0x00, 0x70, 0x00, 0x00, 0xF0, 0x07, 0xFE, 0xE0, 0x07, 0xFE, 0xE0, 0x07,
    0xFE, 0xE0, 0x00, 0x0E, 0xE0, 0x00, 0x0E, 0xE0, 0x00, 0x0E, 0xE0, 0x00,
    0x1C, 0xF0, 0x00, 0x3C, 0x70, 0x00, 0x7C, 0x78, 0x00, 0xFC, 0x3E, 0x03,
    0xDC, 0x1F, 0xFF, 0x98, 0x0F, 0xFE, 0x18, 0x03, 0xF8, 0x18, 0x07, 0x00,
    0x07, 0x83, 0x80, 0x03, 0xC1, 0xC0, 0x01, 0xC0, 0xE0, 0x00, 0xE0, 0xF0,
    0x00, 0x70, 0x70, 0x00, 0x78, 0x38, 0x00, 0x3C, 0x1C, 0x00, 0x1C, 0x1E,
    0x00, 0x0E, 0x0F, 0x00, 0x07, 0x07, 0x00, 0x07, 0x83, 0xFF, 0xFF, 0x81,
    0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xE0, 0xE0, 0x00, 0x70, 0x70, 0x00, 0x78,
    0x38, 0x00, 0x38, 0x1C, 0x00, 0x1C, 0x1E, 0x00, 0x0E, 0x0E, 0x00, 0x0F,
    0x07, 0x00, 0x07, 0x83, 0x80, 0x03, 0x81, 0xC0, 0x01, 0xC1, 0xE0, 0x00,
    0xE0, 0xE0, 0x00, 0xF0, 0x70, 0x00, 0x78, 0x00, 0x07, 0x0F, 0x0F, 0x0E,
    0x0E, 0x0E, 0x0E, 0x1E, 0x1C, 0x1C, 0x1C, 0x1C, 0x3C, 0x3C, 0x38, 0x38,
    0x38, 0x38, 0x78, 0x70, 0x70, 0x70, 0x70, 0xF0, 0xF0, 0xE0, 0x00, 0x01,
    0xC0, 0x00, 0x70, 0x00, 0x3C, 0x00, 0x0E, 0x00, 0x03, 0x80, 0x00, 0xE0,
    0x00, 0x38, 0x00, 0x1E, 0x00, 0x07, 0x00, 0x01, 0xC0, 0x00, 0x70, 0x00,
    0x1C, 0x00, 0x0E, 0x00, 0x03, 0x80, 0x00, 0xE0, 0x00, 0x38, 0x00, 0x1E,
    0x1C, 0x07, 0x0E, 0x01, 0xC3, 0x80, 0x70, 0xE0, 0x3C, 0x38, 0x0E, 0x0F,
    0x0F, 0x81, 0xFF, 0xC0, 0x7F, 0xE0, 0x07, 0xE0, 0x00, 0x07, 0x00, 0x07,
    0x83, 0x80, 0x07, 0x81, 0xC0, 0x0F, 0x00, 0xE0, 0x0F, 0x00, 0xF0, 0x0F,
    0x00, 0x70, 0x0F, 0x00, 0x38, 0x0F, 0x00, 0x1C, 0x1F, 0x00, 0x1E, 0x1E,
    0x00, 0x0F, 0x1E, 0x00, 0x07, 0x1E, 0x00, 0x03, 0x9F, 0x00, 0x01, 0xDF,
    0xC0, 0x01, 0xFC, 0xE0, 0x00, 0xFC, 0x78, 0x00, 0x7C, 0x1C, 0x00, 0x3C,
    0x0F, 0x00, 0x1C, 0x07, 0x80, 0x1E, 0x01, 0xE0, 0x0E, 0x00, 0xF0, 0x07,
    0x00, 0x38, 0x03, 0x80, 0x1E, 0x01, 0xC0, 0x07, 0x01, 0xE0, 0x03, 0xC0,
    0xE0, 0x00, 0xE0, 0x70, 0x00, 0x78, 0x00, 0x07, 0x00, 0x07, 0x00, 0x07,
    0x00, 0x07, 0x00, 0x0F, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x1E,
    0x00, 0x1E, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x3C, 0x00, 0x38,
    0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x78, 0x00, 0x70, 0x00, 0x70,
    0x00, 0x70, 0x00, 0x70, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x07,
    0xC0, 0x00, 0xF8, 0x3E, 0x00, 0x07, 0xC1, 0xF0, 0x00, 0x7E, 0x0F, 0x80,
    0x03, 0xF0, 0xFC, 0x00, 0x3F, 0x07, 0x70, 0x01, 0xF8, 0x3B, 0x80, 0x1D,
    0xC1, 0xDC, 0x00, 0xEE, 0x0E, 0xE0, 0x0E, 0xE0, 0xE7, 0x00, 0x77, 0x07,
    0x38, 0x07, 0x38, 0x39, 0xC0, 0x31, 0xC1, 0xCE, 0x03, 0x9E, 0x1E, 0x38,
    0x38, 0xE0, 0xE1, 0xC1, 0xC7, 0x07, 0x0E, 0x1C, 0x38, 0x38, 0x70, 0xE1,
    0xC1, 0xC3, 0x8E, 0x1E, 0x1E, 0x1C, 0x70, 0xE0, 0xE0, 0xE7, 0x07, 0x07,
    0x07, 0x38, 0x38, 0x38, 0x1F, 0x81, 0xC1, 0xC0, 0xF8, 0x1E, 0x1C, 0x07,
    0xC0, 0xE0, 0xE0, 0x3C, 0x07, 0x07, 0x01, 0xE0, 0x38, 0x00, 0x07, 0x80,
    0x03, 0x83, 0xE0, 0x01, 0xC1, 0xF0, 0x00, 0xE0, 0xF8, 0x00, 0xE0, 0xFE,
    0x00, 0x70, 0x7F, 0x00, 0x38, 0x3B, 0xC0, 0x1C, 0x1D, 0xE0, 0x1E, 0x0E,
    0x70, 0x0E, 0x0E, 0x3C, 0x07, 0x07, 0x0E, 0x03, 0x83, 0x87, 0x81, 0xC1,
    0xC3, 0xC1, 0xE1, 0xE0, 0xE0, 0xE0, 0xE0, 0x78, 0x70, 0x70, 0x1C, 0x38,
    0x38, 0x0F, 0x1C, 0x1C, 0x07, 0x9E, 0x1E, 0x01, 0xCE, 0x0E, 0x00, 0xF7,
    0x07, 0x00, 0x3B, 0x83, 0x80, 0x1F, 0xC1, 0xC0, 0x07, 0xC1, 0xC0, 0x03,
    0xE0, 0xE0, 0x01, 0xF0, 0x70, 0x00, 0x78, 0x00, 0x00, 0x1F, 0xC0, 0x00,
    0xFF, 0xF0, 0x01, 0xFF, 0xF8, 0x03, 0xE0, 0x7C, 0x07, 0x80, 0x1E, 0x0F,
    0x00, 0x0E, 0x1C, 0x00, 0x0F, 0x3C, 0x00, 0x07, 0x38, 0x00, 0x07, 0x70,
    0x00, 0x07, 0x70, 0x00, 0x07, 0x70, 0x00, 0x07, 0xE0, 0x00, 0x07, 0xE0,
    0x00, 0x0F, 0xE0, 0x00, 0x0E, 0xE0, 0x00, 0x0E, 0xE0, 0x00, 0x0E, 0xE0,
    0x00, 0x1C, 0xE0, 0x00, 0x1C, 0xF0, 0x00, 0x38, 0x70, 0x00, 0x78, 0x78,
    0x00, 0xF0, 0x3E, 0x07, 0xE0, 0x1F, 0xFF, 0xC0, 0x0F, 0xFF, 0x00, 0x03,
    0xF8, 0x00, 0x07, 0xFF, 0xE0, 0x1F, 0xFF, 0xC0, 0x7F, 0xFF, 0x81, 0xC0,
    0x1F, 0x0F, 0x00, 0x3C, 0x38, 0x00, 0x70, 0xE0, 0x01, 0xC3, 0x80, 0x07,
    0x1E, 0x00, 0x1C, 0x78, 0x00, 0xE1, 0xC0, 0x07, 0x87, 0x00, 0x3C, 0x1F,
    0xFF, 0xE0, 0xFF, 0xFF, 0x03, 0xFF, 0xF0, 0x0E, 0x00, 0x00, 0x38, 0x00,
    0x00, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1C, 0x00, 0x00, 0x70, 0x00, 0x01,
    0xC0, 0x00, 0x07, 0x00, 0x00, 0x3C, 0x00, 0x00, 0xE0, 0x00, 0x03, 0x80,
    0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x3F, 0xFC, 0x00, 0x7F, 0xFF, 0x00,
    0x7C, 0x07, 0xC0, 0x78, 0x00, 0xF0, 0x78, 0x00, 0x38, 0x78, 0x00, 0x1E,
    0x78, 0x00, 0x07, 0x38, 0x00, 0x03, 0xBC, 0x00, 0x01, 0xDC, 0x00, 0x00,
    0xEE, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x1F, 0x80, 0x00,
    0x1D, 0xC0, 0x00, 0x0E, 0xE0, 0x00, 0x0F, 0x70, 0x00, 0x07, 0x38, 0x00,
    0x87, 0x9E, 0x00, 0xE7, 0x87, 0x00, 0x7F, 0x83, 0xC0, 0x1F, 0x80, 0xF8,
    0x1F, 0x80, 0x3F, 0xFF, 0xE0, 0x0F, 0xFF, 0x78, 0x01, 0xFE, 0x1E, 0x00,
    0x00, 0x07, 0x00, 0x00, 0x02, 0x00, 0x07, 0xFF, 0xF0, 0x0F, 0xFF, 0xF8,
    0x1F, 0xFF, 0xF0, 0x38, 0x00, 0xF0, 0xF0, 0x00, 0xE1, 0xC0, 0x01, 0xC3,
    0x80, 0x03, 0x87, 0x00, 0x07, 0x1E, 0x00, 0x0E, 0x3C, 0x00, 0x38, 0x70,
    0x00, 0xF0, 0xE0, 0x03, 0xC1, 0xFF, 0xFE, 0x07, 0xFF, 0xF8, 0x0F, 0xFF,
    0xF8, 0x1C, 0x00, 0x78, 0x38, 0x00, 0x70, 0x70, 0x00, 0xE1, 0xE0, 0x01,
    0xC3, 0x80, 0x03, 0x87, 0x00, 0x06, 0x0E, 0x00, 0x1C, 0x1C, 0x00, 0x38,
    0x78, 0x00, 0x70, 0xE0, 0x00, 0xE1, 0xC0, 0x01, 0xE0, 0x00, 0x3F, 0xC0,
    0x07, 0xFF, 0xC0, 0x3F, 0xFF, 0x81, 0xF0, 0x1E, 0x0F, 0x00, 0x3C, 0x38,
    0x00, 0x71, 0xC0, 0x01, 0xC7, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x78, 0x00,
    0x01, 0xF8, 0x00, 0x03, 0xFC, 0x00, 0x07, 0xFE, 0x00, 0x07, 0xFF, 0x00,
    0x03, 0xFE, 0x00, 0x00, 0xFC, 0x00, 0x00, 0xF3, 0x80, 0x01, 0xCE, 0x00,
    0x07, 0x38, 0x00, 0x18, 0xE0, 0x00, 0xE3, 0xC0, 0x07, 0x07, 0x80, 0x7C,
    0x1F, 0xFF, 0xE0, 0x3F, 0xFE, 0x00, 0x3F, 0xC0, 0x00, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x1E, 0x00,
    0x01, 0xE0, 0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x03, 0xC0,
    0x00, 0x38, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00, 0x03, 0x80, 0x00, 0x78,
    0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0xF0, 0x00, 0x0F,
    0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x1E, 0x00, 0x01,
    0xE0, 0x00, 0x0E, 0x00, 0x0F, 0x0E, 0x00, 0x0F, 0x0E, 0x00, 0x0E, 0x0E,
    0x00, 0x0E, 0x1E, 0x00, 0x0E, 0x1C, 0x00, 0x1E, 0x1C, 0x00, 0x1C, 0x1C,
    0x00, 0x1C, 0x3C, 0x00, 0x1C, 0x3C, 0x00, 0x1C, 0x38, 0x00, 0x3C, 0x38,
    0x00, 0x38, 0x38, 0x00, 0x38, 0x78, 0x00, 0x38, 0x70, 0x00, 0x78, 0x70,
    0x00, 0x78, 0x70, 0x00, 0x70, 0xF0, 0x00, 0x70, 0xF0, 0x00, 0x70, 0xE0,
    0x00, 0xF0, 0xE0, 0x00, 0xE0, 0xF0, 0x03, 0xE0, 0x78, 0x0F, 0xC0, 0x7F,
    0xFF, 0x80, 0x1F, 0xFE, 0x00, 0x07, 0xF0, 0x00, 0xE0, 0x00, 0x3F, 0x80,
    0x03, 0xFC, 0x00, 0x1D, 0xE0, 0x01, 0xE7, 0x00, 0x0E, 0x38, 0x00, 0xE1,
    0xC0, 0x07, 0x0E, 0x00, 0x70, 0x70, 0x07, 0x83, 0xC0, 0x38, 0x1E, 0x03,
    0xC0, 0xF0, 0x1C, 0x03, 0x81, 0xE0, 0x1C, 0x0E, 0x00, 0xE0, 0xF0, 0x07,
    0x07, 0x00, 0x3C, 0x70, 0x01, 0xE3, 0x80, 0x0F, 0x38, 0x00, 0x39, 0xC0,
    0x01, 0xDC, 0x00, 0x0E, 0xE0, 0x00, 0x7E, 0x00, 0x03, 0xF0, 0x00, 0x1F,
    0x00, 0x00, 0xF0, 0x00, 0x00, 0xE0, 0x03, 0x80, 0x0E, 0xE0, 0x07, 0x80,
    0x1E, 0xE0, 0x07, 0xC0, 0x1E, 0xE0, 0x0F, 0xC0, 0x1C, 0xE0, 0x0F, 0xC0,
    0x3C, 0xE0, 0x1F, 0xC0, 0x38, 0xE0, 0x1D, 0xC0, 0x78, 0xE0, 0x3D, 0xC0,
    0x70, 0xE0, 0x39, 0xC0, 0xF0, 0xE0, 0x79, 0xC0, 0xE0, 0xE0, 0x71, 0xC0,
    0xE0, 0xE0, 0xF1, 0xC1, 0xC0, 0xE0, 0xE1, 0xC1, 0xC0, 0xE1, 0xE1, 0xC3,
    0xC0, 0x61, 0xC1, 0xC3, 0x80, 0x63, 0xC1, 0xC7, 0x80, 0x63, 0x80, 0xE7,
    0x00, 0x67, 0x80, 0xEF, 0x00, 0x67, 0x00, 0xEE, 0x00, 0x7F, 0x00, 0xEE,
    0x00, 0x7E, 0x00, 0xFC, 0x00, 0x7E, 0x00, 0xFC, 0x00, 0x7C, 0x00, 0xF8,
    0x00, 0x7C, 0x00, 0xF8, 0x00, 0x78, 0x00, 0xF8, 0x00, 0x78, 0x00, 0xF0,
    0x00, 0x03, 0xC0, 0x03, 0xC0, 0x78, 0x00, 0xF0, 0x07, 0x80, 0x1C, 0x00,
    0xF0, 0x07, 0x80, 0x0F, 0x01, 0xE0, 0x01, 0xE0, 0x78, 0x00, 0x1C, 0x1E,
    0x00, 0x03, 0xC7, 0x80, 0x00, 0x39, 0xE0, 0x00, 0x07, 0xB8, 0x00, 0x00,
    0x7E, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x3E, 0x00,
    0x00, 0x0F, 0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0xF3, 0x80, 0x00, 0x3C,
    0x78, 0x00, 0x0F, 0x0F, 0x00, 0x03, 0xC0, 0xF0, 0x00, 0x70, 0x1E, 0x00,
    0x1E, 0x01, 0xE0, 0x07, 0x80, 0x3C, 0x01, 0xE0, 0x03, 0xC0, 0x78, 0x00,
    0x78, 0x1E, 0x00, 0x0F, 0x00, 0xF0, 0x00, 0x3C, 0xE0, 0x00, 0x71, 0xE0,
    0x01, 0xE3, 0xC0, 0x07, 0x83, 0xC0, 0x1E, 0x07, 0x80, 0x78, 0x07, 0x00,
    0xE0, 0x0F, 0x03, 0xC0, 0x1E, 0x0F, 0x00, 0x1C, 0x3C, 0x00, 0x3C, 0xF0,
    0x00, 0x39, 0xC0, 0x00, 0x7F, 0x80, 0x00, 0xFE, 0x00, 0x00, 0xF8, 0x00,
    0x01, 0xE0, 0x00, 0x03, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x00,
    0x1C, 0x00, 0x00, 0x78, 0x00, 0x00, 0xF0, 0x00, 0x01, 0xC0, 0x00, 0x03,
    0x80, 0x00, 0x07, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x81,
    0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xE0, 0xFF,
    0xFF, 0xF0, 0x7F, 0xFF, 0xF8, 0x00, 0x01, 0xF8, 0x1F, 0xC0, 0xFE, 0x07,
    0x00, 0x38, 0x03, 0xC0, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x03, 0xC0,
    0x1C, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07,
    0x00, 0x38, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x78, 0x03, 0x80,
    0x1C, 0x00, 0xE0, 0x07, 0x00, 0x70, 0x03, 0xF8, 0x1F, 0xC0, 0xFE, 0x00,
    0xCC, 0xCC, 0xCC, 0x46, 0x66, 0x66, 0x66, 0x66, 0x66, 0x62, 0x33, 0x33,
    0x33, 0x03, 0xF8, 0x1F, 0xC0, 0xFE, 0x00, 0x70, 0x07, 0x00, 0x38, 0x01,
    0xC0, 0x0E, 0x00, 0xF0, 0x07, 0x00, 0x38, 0x01, 0xC0, 0x0E, 0x00, 0xE0,
    0x07, 0x00, 0x38, 0x01, 0xC0, 0x0E, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x01,
    0xC0, 0x1E, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x01, 0xC0, 0x1E, 0x00, 0xE0,
    0x07, 0x03, 0xF8, 0x1F, 0xC0, 0xFC, 0x00, 0x00, 0xF0, 0x03, 0xC0, 0x1F,
    0x00, 0x7C, 0x03, 0xB8, 0x1C, 0xE0, 0x63, 0x83, 0x8E, 0x1C, 0x38, 0x60,
    0x73, 0x81, 0xCC, 0x07, 0x70, 0x1F, 0x80, 0x70, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xC0, 0xF1, 0xC3, 0x86, 0x0C, 0x00, 0xFE, 0x01, 0xFF, 0xE0, 0xFF,
    0xFC, 0x3C, 0x0F, 0x1C, 0x01, 0xC0, 0x00, 0x70, 0x00, 0x1C, 0x00, 0x0E,
    0x00, 0x1F, 0x83, 0xFF, 0xE3, 0xFE, 0x39, 0xF0, 0x1E, 0xF0, 0x07, 0x38,
    0x01, 0xCE, 0x00, 0xF3, 0xC0, 0xFC, 0xFF, 0xF7, 0x9F, 0xF1, 0xE1, 0xF0,
    0x38, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0xF0,
    0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0x71, 0xF0, 0x0E, 0xFF, 0x83, 0xFF,
    0xF8, 0x7F, 0x0F, 0x0F, 0x80, 0xF1, 0xE0, 0x0E, 0x38, 0x01, 0xCF, 0x00,
    0x39, 0xE0, 0x07, 0x38, 0x00, 0xE7, 0x00, 0x38, 0xE0, 0x07, 0x3C, 0x00,
    0xE7, 0x80, 0x38, 0xF8, 0x0F, 0x1F, 0x87, 0xC3, 0xFF, 0xF0, 0xE7, 0xFC,
    0x1C, 0x7E, 0x00, 0x01, 0xF8, 0x07, 0xFC, 0x0F, 0xFE, 0x1E, 0x0F, 0x3C,
    0x07, 0x78, 0x07, 0x70, 0x07, 0x70, 0x00, 0xF0, 0x00, 0xE0, 0x00, 0xE0,
    0x00, 0xE0, 0x00, 0xE0, 0x0E, 0xE0, 0x1C, 0xF0, 0x3C, 0x78, 0x78, 0x7F,
    0xF0, 0x3F, 0xE0, 0x0F, 0x80, 0x00, 0x00, 0x70, 0x00, 0x0F, 0x00, 0x00,
    0xE0, 0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00, 0x01, 0xE0, 0x1F,
    0x1C, 0x07, 0xFD, 0xC0, 0xFF, 0xDC, 0x1E, 0x0F, 0xC3, 0xC0, 0x7C, 0x38,
    0x07, 0x87, 0x00, 0x38, 0x70, 0x03, 0x8F, 0x00, 0x38, 0xE0, 0x07, 0x8E,
    0x00, 0x70, 0xE0, 0x07, 0x0E, 0x00, 0xF0, 0xE0, 0x0F, 0x0F, 0x01, 0xF0,
    0x78, 0x7E, 0x07, 0xFF, 0xE0, 0x3F, 0xEE, 0x01, 0xF8, 0xE0, 0x01, 0xF8,
    0x03, 0xFF, 0x03, 0xFF, 0xC3, 0xC1, 0xF3, 0xC0, 0x79, 0xC0, 0x1D, 0xC0,
    0x0E, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x1C, 0x00, 0x0E,
    0x00, 0x07, 0x00, 0x73, 0xC0, 0x78, 0xF0, 0x78, 0x7F, 0xF8, 0x1F, 0xF8,
    0x03, 0xF0, 0x00, 0x01, 0xE0, 0x7C, 0x1F, 0x83, 0x80, 0x70, 0x1C, 0x03,
    0x83, 0xFC, 0x7F, 0x8F, 0xF0, 0x70, 0x0E, 0x01, 0xC0, 0x38, 0x0F, 0x01,
    0xC0, 0x38, 0x07, 0x00, 0xE0, 0x38, 0x07, 0x00, 0xE0, 0x1C, 0x03, 0x80,
    0xE0, 0x1C, 0x00, 0x00, 0xFC, 0x60, 0x7F, 0xCC, 0x1F, 0xFF, 0x87, 0xC3,
    0xF1, 0xE0, 0x3E, 0x38, 0x03, 0x8E, 0x00, 0x71, 0xC0, 0x0E, 0x38, 0x01,
    0xCE, 0x00, 0x79, 0xC0, 0x0E, 0x38, 0x01, 0xC7, 0x00, 0x78, 0xE0, 0x0F,
    0x1E, 0x03, 0xC1, 0xE1, 0xF8, 0x3F, 0xFF, 0x03, 0xFE, 0xE0, 0x1F, 0x1C,
    0x00, 0x03, 0x00, 0x00, 0xE0, 0x00, 0x18, 0x38, 0x07, 0x07, 0x83, 0xC0,
    0x7F, 0xF8, 0x0F, 0xFC, 0x00, 0x7E, 0x00, 0x00, 0x07, 0x00, 0x01, 0xC0,
    0x00, 0x70, 0x00, 0x1C, 0x00, 0x0F, 0x00, 0x03, 0x80, 0x00, 0xE0, 0x00,
    0x38, 0xFC, 0x0E, 0xFF, 0x87, 0xFF, 0xF1, 0xF8, 0x3C, 0x7C, 0x07, 0x1E,
    0x01, 0xC7, 0x00, 0x73, 0xC0, 0x1C, 0xE0, 0x0F, 0x38, 0x03, 0x8E, 0x00,
    0xE3, 0x80, 0x39, 0xE0, 0x0E, 0x70, 0x07, 0x9C, 0x01, 0xC7, 0x00, 0x71,
    0xC0, 0x1C, 0xE0, 0x07, 0x38, 0x03, 0x80, 0x07, 0x07, 0x0F, 0x0E, 0x00,
    0x00, 0x00, 0x1E, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x38, 0x38, 0x38, 0x38,
    0x38, 0x78, 0x70, 0x70, 0x70, 0x70, 0xF0, 0xE0, 0xE0, 0x00, 0x3C, 0x00,
    0xE0, 0x03, 0x80, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x00,
    0x70, 0x01, 0xC0, 0x0E, 0x00, 0x38, 0x00, 0xE0, 0x03, 0x80, 0x1E, 0x00,
    0x70, 0x01, 0xC0, 0x07, 0x00, 0x1C, 0x00, 0xE0, 0x03, 0x80, 0x0E, 0x00,
    0x38, 0x00, 0xE0, 0x07, 0x00, 0x1C, 0x00, 0x70, 0x01, 0xC0, 0x0F, 0x00,
    0x38, 0x00, 0xE0, 0x1F, 0x80, 0x7C, 0x03, 0xE0, 0x00, 0x07, 0x00, 0x00,
    0xE0, 0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0xF0, 0x00, 0x1C, 0x00, 0x03,
    0x80, 0x00, 0x70, 0x1E, 0x0E, 0x07, 0x83, 0xC1, 0xE0, 0x70, 0x70, 0x0E,
    0x1C, 0x01, 0xCF, 0x00, 0x3B, 0xC0, 0x0F, 0xF8, 0x01, 0xFF, 0x80, 0x3E,
    0x70, 0x07, 0x8E, 0x00, 0xE0, 0xE0, 0x38, 0x1C, 0x07, 0x03, 0xC0, 0xE0,
    0x38, 0x1C, 0x07, 0x03, 0x80, 0xF0, 0xE0, 0x0E, 0x1C, 0x01, 0xE0, 0x07,
    0x07, 0x0F, 0x0E, 0x0E, 0x0E, 0x0E, 0x1E, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
    0x38, 0x38, 0x38, 0x38, 0x38, 0x78, 0x70, 0x70, 0x70, 0x70, 0xF0, 0xE0,
    0xE0, 0x1E, 0x7C, 0x0F, 0x83, 0xBF, 0xE7, 0xF8, 0x7F, 0xFD, 0xFF, 0x8F,
    0xC3, 0xF0, 0xF1, 0xE0, 0x3C, 0x0E, 0x38, 0x07, 0x01, 0xCF, 0x01, 0xE0,
    0x39, 0xC0, 0x38, 0x07, 0x38, 0x07, 0x00, 0xE7, 0x00, 0xE0, 0x1C, 0xE0,
    0x1C, 0x07, 0x3C, 0x07, 0x00, 0xE7, 0x00, 0xE0, 0x1C, 0xE0, 0x1C, 0x03,
    0x9C, 0x03, 0x80, 0xF3, 0x80, 0x70, 0x1C, 0x70, 0x1C, 0x03, 0x9C, 0x03,
    0x80, 0x73, 0x80, 0x70, 0x0E, 0x00, 0x1E, 0x3E, 0x07, 0x7F, 0xE1, 0xFF,
    0xF8, 0x7E, 0x0F, 0x1F, 0x01, 0xC7, 0x80, 0x73, 0xC0, 0x1C, 0xE0, 0x07,
    0x38, 0x03, 0xCE, 0x00, 0xE3, 0x80, 0x39, 0xE0, 0x0E, 0x70, 0x03, 0x9C,
    0x01, 0xC7, 0x00, 0x71, 0xC0, 0x1C, 0x70, 0x07, 0x38, 0x01, 0xCE, 0x00,
    0xE0, 0x01, 0xF8, 0x03, 0xFF, 0x03, 0xFF, 0xC3, 0xE1, 0xE3, 0xC0, 0x79,
    0xC0, 0x1D, 0xC0, 0x0E, 0xE0, 0x07, 0x70, 0x03, 0xF0, 0x01, 0xF8, 0x01,
    0xDC, 0x00, 0xEE, 0x00, 0x77, 0x00, 0x73, 0xC0, 0x78, 0xF0, 0xF8, 0x7F,
    0xF8, 0x1F, 0xF8, 0x03, 0xF0, 0x00, 0x03, 0x8F, 0x80, 0x1D, 0xFF, 0x01,
    0xFF, 0xFC, 0x0F, 0xC1, 0xE0, 0x7C, 0x07, 0x83, 0xC0, 0x1C, 0x1C, 0x00,
    0xE1, 0xE0, 0x07, 0x0E, 0x00, 0x38, 0x70, 0x01, 0xC3, 0x80, 0x1E, 0x1C,
    0x00, 0xE1, 0xE0, 0x07, 0x0F, 0x00, 0x70, 0x78, 0x07, 0x83, 0xF0, 0xF8,
    0x3F, 0xFF, 0x81, 0xDF, 0xF8, 0x0E, 0x3F, 0x00, 0x70, 0x00, 0x03, 0x80,
    0x00, 0x3C, 0x00, 0x01, 0xC0, 0x00, 0x0E, 0x00, 0x00, 0x70, 0x00, 0x03,
    0x80, 0x00, 0x00, 0x00, 0xF8, 0xF0, 0x7F, 0xEE, 0x0F, 0xFF, 0xE1, 0xF0,
    0xFE, 0x3C, 0x07, 0xE3, 0x80, 0x3E, 0x70, 0x03, 0xC7, 0x00, 0x3C, 0x70,
    0x03, 0xCE, 0x00, 0x3C, 0xE0, 0x07, 0x8E, 0x00, 0x78, 0xE0, 0x07, 0x8E,
    0x00, 0xF8, 0xF0, 0x1F, 0x87, 0x87, 0xF0, 0x7F, 0xF7, 0x03, 0xFE, 0x70,
    0x0F, 0x8F, 0x00, 0x00, 0xF0, 0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x0E,
    0x00, 0x01, 0xE0, 0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x04, 0x00, 0x1E,
    0x78, 0xE7, 0xC7, 0x7C, 0x3F, 0x01, 0xF0, 0x0F, 0x00, 0xF0, 0x07, 0x00,
    0x38, 0x01, 0xC0, 0x0E, 0x00, 0xF0, 0x07, 0x00, 0x38, 0x01, 0xC0, 0x0E,
    0x00, 0x70, 0x07, 0x00, 0x38, 0x00, 0x01, 0xF8, 0x07, 0xFE, 0x0F, 0xFF,
    0x1E, 0x0F, 0x3C, 0x07, 0x38, 0x07, 0x38, 0x00, 0x3C, 0x00, 0x3F, 0x80,
    0x1F, 0xF8, 0x07, 0xFC, 0x00, 0x7E, 0x00, 0x0E, 0xE0, 0x0E, 0xE0, 0x1E,
    0xF0, 0x3C, 0x7F, 0xF8, 0x7F, 0xF0, 0x1F, 0xC0, 0x0E, 0x03, 0x80, 0xE0,
    0x38, 0x7F, 0xDF, 0xEF, 0xF8, 0x70, 0x1C, 0x0E, 0x03, 0x80, 0xE0, 0x38,
    0x1E, 0x07, 0x01, 0xC0, 0x70, 0x1C, 0x0F, 0x03, 0x80, 0xFC, 0x3F, 0x07,
    0x80, 0x1C, 0x03, 0xC7, 0x00, 0xE1, 0xC0, 0x38, 0xF0, 0x0E, 0x38, 0x03,
    0x8E, 0x00, 0xE3, 0x80, 0x70, 0xE0, 0x1C, 0x78, 0x07, 0x1C, 0x01, 0xC7,
    0x00, 0x71, 0xC0, 0x3C, 0x70, 0x0E, 0x38, 0x07, 0x8E, 0x03, 0xE3, 0x81,
    0xF8, 0xFF, 0xFE, 0x1F, 0xFF, 0x03, 0xF1, 0xC0, 0xE0, 0x07, 0xE0, 0x0F,
    0xE0, 0x0E, 0xE0, 0x1C, 0x70, 0x1C, 0x70, 0x38, 0x70, 0x38, 0x70, 0x70,
    0x70, 0xF0, 0x70, 0xE0, 0x71, 0xC0, 0x71, 0xC0, 0x33, 0x80, 0x3B, 0x80,
    0x3F, 0x00, 0x3F, 0x00, 0x3E, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0xE0, 0x1C,
    0x07, 0xE0, 0x3C, 0x0E, 0xE0, 0x3C, 0x0E, 0xE0, 0x7C, 0x1C, 0xE0, 0x7C,
    0x1C, 0xE0, 0xEC, 0x38, 0xE0, 0xEC, 0x38, 0x61, 0xCC, 0x70, 0x61, 0xCC,
    0x70, 0x63, 0x8C, 0xE0, 0x73, 0x8C, 0xE0, 0x77, 0x0C, 0xC0, 0x77, 0x0D,
    0xC0, 0x7E, 0x0D, 0x80, 0x7E, 0x0F, 0x80, 0x7C, 0x0F, 0x80, 0x7C, 0x0F,
    0x00, 0x78, 0x0F, 0x00, 0x78, 0x0E, 0x00, 0x0E, 0x00, 0xE1, 0xE0, 0x38,
    0x1C, 0x0E, 0x03, 0xC3, 0x80, 0x38, 0xE0, 0x07, 0xBC, 0x00, 0x77, 0x00,
    0x0F, 0xC0, 0x00, 0xF0, 0x00, 0x1C, 0x00, 0x07, 0xC0, 0x01, 0xF8, 0x00,
    0x77, 0x80, 0x1E, 0x70, 0x07, 0x8F, 0x00, 0xE0, 0xE0, 0x38, 0x1C, 0x0E,
    0x01, 0xC3, 0x80, 0x38, 0x00, 0x0E, 0x00, 0x70, 0xF0, 0x0F, 0x07, 0x00,
    0xE0, 0x70, 0x1C, 0x07, 0x01, 0xC0, 0x70, 0x38, 0x07, 0x03, 0x80, 0x70,
    0x70, 0x07, 0x07, 0x00, 0x70, 0xE0, 0x03, 0x9E, 0x00, 0x39, 0xC0, 0x03,
    0xB8, 0x00, 0x3B, 0x80, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x03, 0xE0, 0x00,
    0x1E, 0x00, 0x01, 0xC0, 0x00, 0x38, 0x00, 0x03, 0x80, 0x00, 0x70, 0x00,
    0x07, 0x00, 0x00, 0xE0, 0x00, 0xFE, 0x00, 0x0F, 0xC0, 0x00, 0xF0, 0x00,
    0x00, 0x07, 0xFF, 0xC0, 0xFF, 0xF8, 0x3F, 0xFF, 0x00, 0x01, 0xC0, 0x00,
    0x70, 0x00, 0x1C, 0x00, 0x07, 0x00, 0x01, 0xC0, 0x00, 0x70, 0x00, 0x1C,
    0x00, 0x07, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00,
    0x03, 0xC0, 0x00, 0x7F, 0xFE, 0x1F, 0xFF, 0xC3, 0xFF, 0xF8, 0x00, 0x00,
    0x70, 0x1F, 0x01, 0xF0, 0x3C, 0x03, 0x80, 0x38, 0x07, 0x00, 0x70, 0x07,
    0x00, 0x70, 0x07, 0x00, 0xE0, 0x0E, 0x01, 0xE0, 0x3C, 0x0F, 0x80, 0xE0,
    0x0F, 0x00, 0x78, 0x03, 0x80, 0x38, 0x03, 0x80, 0x38, 0x03, 0x80, 0x38,
    0x07, 0x00, 0x70, 0x07, 0x00, 0x70, 0x0E, 0x00, 0xF8, 0x0F, 0x80, 0x78,
    0x00, 0x01, 0x80, 0xC0, 0xC0, 0x60, 0x30, 0x18, 0x0C, 0x0C, 0x06, 0x03,
    0x01, 0x81, 0xC0, 0xC0, 0x60, 0x30, 0x18, 0x18, 0x0C, 0x06, 0x03, 0x01,
    0x81, 0x80, 0xC0, 0x60, 0x30, 0x38, 0x18, 0x0C, 0x06, 0x03, 0x03, 0x01,
    0x80, 0xC0, 0x00, 0x01, 0xE0, 0x1F, 0x01, 0xF0, 0x07, 0x00, 0xE0, 0x0E,
    0x00, 0xE0, 0x0E, 0x01, 0xC0, 0x1C, 0x01, 0xC0, 0x1C, 0x01, 0xC0, 0x1C,
    0x01, 0xE0, 0x0F, 0x00, 0x70, 0x1F, 0x03, 0xC0, 0x78, 0x07, 0x00, 0x70,
    0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x01, 0xC0, 0x1C, 0x03, 0xC0,
    0xF8, 0x0F, 0x80, 0xE0, 0x00, 0x1C, 0x00, 0x3F, 0x00, 0x7F, 0x83, 0x63,
    0xC7, 0xC1, 0xFE, 0x00, 0xFC, 0x00, 0x78};

const GFXglyph FreeSansOblique18pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 10, 0, 1},         // 0x20 ' '
    {0, 9, 26, 10, 4, -25},      // 0x21 '!'
    {30, 10, 9, 12, 6, -24},     // 0x22 '"'
    {42, 21, 25, 19, 2, -24},    // 0x23 '#'
    {108, 20, 31, 19, 2, -26},   // 0x24 '$'
    {186, 26, 25, 31, 5, -24},   // 0x25 '%'
    {268, 20, 25, 23, 3, -24},   // 0x26 '&'
    {331, 4, 9, 7, 6, -24},      // 0x27 '''
    {336, 12, 33, 12, 4, -25},   // 0x28 '('
    {386, 12, 33, 12, -1, -24},  // 0x29 ')'
    {436, 10, 10, 14, 6, -25},   // 0x2A '*'
    {449, 18, 16, 20, 3, -15},   // 0x2B '+'
    {485, 5, 8, 10, 2, -2},      // 0x2C ','
    {490, 9, 3, 12, 3, -10},     // 0x2D '-'
    {494, 4, 4, 10, 3, -3},      // 0x2E '.'
    {496, 15, 26, 10, 0, -25},   // 0x2F '/'
    {545, 18, 25, 19, 3, -24},   // 0x30 '0'
    {602, 10, 25, 19, 7, -24},   // 0x31 '1'
    {634, 20, 25, 19, 2, -24},   // 0x32 '2'
    {697, 19, 25, 19, 2, -24},   // 0x33 '3'
    {757, 18, 25, 19, 2, -24},   // 0x34 '4'
    {814, 20, 25, 19, 2, -24},   // 0x35 '5'
    {877, 19, 25, 19, 3, -24},   // 0x36 '6'
    {937, 18, 25, 19, 5, -24},   // 0x37 '7'
    {994, 19, 25, 19, 3, -24},   // 0x38 '8'
    {1054, 19, 25, 19, 2, -24},  // 0x39 '9'
    {1114, 7, 19, 10, 4, -18},   // 0x3A ':'
    {1131, 8, 24, 10, 3, -18},   // 0x3B ';'
    {1155, 19, 17, 20, 3, -16},  // 0x3C '<'
    {1196, 18, 9, 20, 3, -12},   // 0x3D '='
    {1217, 19, 17, 20, 2, -15},  // 0x3E '>'
    {1258, 16, 26, 19, 6, -25},  // 0x3F '?'
    {1310, 33, 31, 36, 3, -25},  // 0x40 '@'
    {1438, 23, 26, 23, 0, -25},  // 0x41 'A'
    {1513, 21, 26, 23, 3, -25},  // 0x42 'B'
    {1582, 22, 26, 25, 4, -25},  // 0x43 'C'
    {1654, 23, 26, 25, 3, -25},  // 0x44 'D'
    {1729, 23, 26, 23, 3, -25},  // 0x45 'E'
    {1804, 22, 26, 21, 3, -25},  // 0x46 'F'
    {1876, 24, 26, 27, 4, -25},  // 0x47 'G'
    {1954, 25, 26, 25, 3, -25},  // 0x48 'H'
    {2036, 8, 26, 10, 4, -25},   // 0x49 'I'
    {2062, 18, 26, 18, 2, -25},  // 0x4A 'J'
    {2121, 25, 26, 23, 3, -25},  // 0x4B 'K'
    {2203, 16, 26, 19, 3, -25},  // 0x4C 'L'
    {2255, 29, 26, 30, 3, -25},  // 0x4D 'M'
    {2350, 25, 26, 26, 3, -25},  // 0x4E 'N'
    {2432, 24, 26, 27, 4, -25},  // 0x4F 'O'
    {2510, 22, 26, 23, 3, -25},  // 0x50 'P'
    {2582, 25, 28, 27, 4, -25},  // 0x51 'Q'
    {2670, 23, 26, 25, 3, -25},  // 0x52 'R'
    {2745, 22, 26, 23, 3, -25},  // 0x53 'S'
    {2817, 20, 26, 21, 6, -25},  // 0x54 'T'
    {2882, 24, 26, 25, 4, -25},  // 0x55 'U'
    {2960, 21, 26, 23, 6, -25},  // 0x56 'V'
    {3029, 32, 26, 33, 6, -25},  // 0x57 'W'
    {3133, 27, 26, 23, 1, -25},  // 0x58 'X'
    {3221, 23, 26, 24, 6, -25},  // 0x59 'Y'
    {3296, 25, 26, 21, 1, -25},  // 0x5A 'Z'
    {3378, 13, 33, 10, 1, -25},  // 0x5B '['
    {3432, 4, 26, 10, 5, -25},   // 0x5C '\'
    {3445, 13, 33, 10, -1, -24}, // 0x5D ']'
    {3499, 14, 14, 16, 3, -24},  // 0x5E '^'
    {3524, 21, 2, 19, -2, 5},    // 0x5F '_'
    {3530, 6, 5, 12, 6, -25},    // 0x60 '`'
    {3534, 18, 19, 19, 2, -18},  // 0x61 'a'
    {3577, 19, 26, 20, 2, -25},  // 0x62 'b'
    {3639, 16, 19, 18, 3, -18},  // 0x63 'c'
    {3677, 20, 26, 20, 3, -25},  // 0x64 'd'
    {3742, 17, 19, 19, 3, -18},  // 0x65 'e'
    {3783, 11, 26, 9, 2, -25},   // 0x66 'f'
    {3819, 19, 27, 19, 2, -18},  // 0x67 'g'
    {3884, 18, 26, 19, 2, -25},  // 0x68 'h'
    {3943, 8, 26, 8, 2, -25},    // 0x69 'i'
    {3969, 14, 34, 8, -2, -25},  // 0x6A 'j'
    {4029, 19, 26, 18, 2, -25},  // 0x6B 'k'
    {4091, 8, 26, 8, 2, -25},    // 0x6C 'l'
    {4117, 27, 19, 29, 2, -18},  // 0x6D 'm'
    {4182, 18, 19, 19, 2, -18},  // 0x6E 'n'
    {4225, 17, 19, 19, 3, -18},  // 0x6F 'o'
    {4266, 21, 26, 20, 0, -18},  // 0x70 'p'
    {4335, 20, 27, 19, 2, -18},  // 0x71 'q'
    {4403, 13, 19, 11, 2, -18},  // 0x72 'r'
    {4434, 16, 19, 18, 2, -18},  // 0x73 's'
    {4472, 10, 23, 9, 3, -22},   // 0x74 't'
    {4501, 18, 19, 19, 3, -18},  // 0x75 'u'
    {4544, 16, 19, 17, 4, -18},  // 0x76 'v'
    {4582, 24, 19, 25, 4, -18},  // 0x77 'w'
    {4639, 19, 19, 17, 1, -18},  // 0x78 'x'
    {4685, 20, 27, 17, 0, -18},  // 0x79 'y'
    {4753, 19, 19, 17, 1, -18},  // 0x7A 'z'
    {4799, 12, 33, 12, 3, -25},  // 0x7B '{'
    {4849, 9, 33, 9, 2, -25},    // 0x7C '|'
    {4887, 12, 33, 12, 0, -24},  // 0x7D '}'
    {4937, 16, 7, 20, 5, -15}};  // 0x7E '~'

const GFXfont FreeSansOblique18pt7b PROGMEM = {
    (uint8_t *)FreeSansOblique18pt7bBitmaps,
    (GFXglyph *)FreeSansOblique18pt7bGlyphs, 0x20, 0x7E, 42};

// Approx. 5623 bytes
