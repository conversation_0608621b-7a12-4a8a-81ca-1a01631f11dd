#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerifItalic9pt7bBitmaps[] PROGMEM = {
    0x11, 0x12, 0x22, 0x24, 0x40, 0x0C, 0xDE, 0xE5, 0x40, 0x04, 0x82, 0x20,
    0x98, 0x24, 0x7F, 0xC4, 0x82, 0x23, 0xFC, 0x24, 0x11, 0x04, 0x83, 0x20,
    0x1C, 0x1B, 0x99, 0x4D, 0x26, 0x81, 0xC0, 0x70, 0x1C, 0x13, 0x49, 0xA4,
    0xDA, 0xC7, 0xC1, 0x00, 0x80, 0x1C, 0x61, 0xCF, 0x0E, 0x28, 0x30, 0xA0,
    0xC5, 0x03, 0x34, 0xE7, 0xAE, 0x40, 0xB1, 0x05, 0x84, 0x26, 0x20, 0x99,
    0x84, 0x3C, 0x03, 0x80, 0x6C, 0x06, 0xC0, 0x78, 0x06, 0x01, 0xEF, 0x66,
    0x24, 0x24, 0xC3, 0x8C, 0x10, 0xE3, 0x87, 0xCE, 0xFA, 0x08, 0x21, 0x08,
    0x61, 0x8C, 0x30, 0xC3, 0x0C, 0x30, 0x41, 0x02, 0x00, 0x10, 0x40, 0x82,
    0x0C, 0x30, 0xC3, 0x0C, 0x61, 0x84, 0x21, 0x08, 0x00, 0x30, 0xCA, 0x5E,
    0x6A, 0x93, 0x08, 0x08, 0x04, 0x02, 0x01, 0x0F, 0xF8, 0x40, 0x20, 0x10,
    0x08, 0x00, 0x56, 0xF0, 0xF0, 0x03, 0x02, 0x06, 0x04, 0x08, 0x08, 0x10,
    0x30, 0x20, 0x60, 0x40, 0xC0, 0x0E, 0x0C, 0x8C, 0x6C, 0x36, 0x1F, 0x0F,
    0x07, 0x87, 0xC3, 0x61, 0xB1, 0x88, 0x83, 0x80, 0x04, 0x70, 0xC3, 0x08,
    0x21, 0x86, 0x10, 0x43, 0x08, 0xF8, 0x1C, 0x67, 0x83, 0x03, 0x02, 0x06,
    0x0C, 0x08, 0x10, 0x20, 0x42, 0xFC, 0x0F, 0x08, 0xC0, 0x60, 0xC1, 0xE0,
    0x38, 0x0C, 0x06, 0x03, 0x01, 0x01, 0x1F, 0x00, 0x01, 0x01, 0x81, 0x41,
    0x61, 0x21, 0x11, 0x18, 0x88, 0xFF, 0x02, 0x03, 0x01, 0x00, 0x0F, 0x84,
    0x04, 0x03, 0x80, 0x60, 0x18, 0x0C, 0x06, 0x03, 0x03, 0x03, 0x1E, 0x00,
    0x01, 0x83, 0x87, 0x07, 0x03, 0x03, 0x73, 0xCD, 0x86, 0xC3, 0x61, 0xB1,
    0x88, 0xC3, 0xC0, 0x7F, 0x40, 0x80, 0x80, 0x40, 0x40, 0x60, 0x20, 0x20,
    0x10, 0x10, 0x18, 0x08, 0x00, 0x1E, 0x19, 0xCC, 0x66, 0x33, 0xB0, 0xE0,
    0x50, 0xCC, 0xC3, 0x61, 0xB0, 0xCC, 0xC3, 0xC0, 0x0E, 0x19, 0x8C, 0x6C,
    0x36, 0x1B, 0x0D, 0x86, 0xE6, 0x3F, 0x03, 0x03, 0x06, 0x0C, 0x00, 0x33,
    0x00, 0x00, 0xCC, 0x33, 0x00, 0x00, 0x44, 0x48, 0x01, 0x83, 0x86, 0x1C,
    0x0C, 0x03, 0x80, 0x30, 0x07, 0x00, 0x80, 0xFF, 0x80, 0x00, 0x00, 0x0F,
    0xF8, 0xC0, 0x1C, 0x03, 0x80, 0x70, 0x18, 0x38, 0x70, 0xC0, 0x80, 0x00,
    0x3C, 0x8C, 0x18, 0x30, 0xC3, 0x0C, 0x20, 0x40, 0x80, 0x06, 0x00, 0x0F,
    0xC0, 0xC3, 0x0C, 0x04, 0xC7, 0xBC, 0x64, 0xE2, 0x27, 0x31, 0x39, 0x91,
    0xCC, 0x93, 0x3B, 0x0E, 0x00, 0x1F, 0x80, 0x01, 0x00, 0x60, 0x14, 0x04,
    0xC0, 0x98, 0x23, 0x07, 0xE1, 0x04, 0x20, 0x88, 0x1B, 0x8F, 0x80, 0x3F,
    0xC1, 0x8C, 0x21, 0x8C, 0x31, 0x8C, 0x3E, 0x04, 0x61, 0x86, 0x30, 0xC4,
    0x19, 0x86, 0x7F, 0x80, 0x07, 0x91, 0x86, 0x30, 0x26, 0x02, 0x60, 0x0C,
    0x00, 0xC0, 0x0C, 0x00, 0xC0, 0x0C, 0x00, 0x61, 0x83, 0xE0, 0x3F, 0xC0,
    0x63, 0x82, 0x0C, 0x30, 0x31, 0x81, 0x8C, 0x0C, 0x40, 0x66, 0x07, 0x30,
    0x31, 0x03, 0x18, 0x71, 0xFE, 0x00, 0x3F, 0xF0, 0xC2, 0x08, 0x21, 0x80,
    0x19, 0x81, 0xF8, 0x11, 0x03, 0x10, 0x30, 0x02, 0x04, 0x60, 0x8F, 0xF8,
    0x3F, 0xF0, 0xC2, 0x08, 0x21, 0x80, 0x19, 0x81, 0xF8, 0x11, 0x03, 0x10,
    0x30, 0x02, 0x00, 0x60, 0x0F, 0x80, 0x07, 0x91, 0x87, 0x30, 0x26, 0x02,
    0x60, 0x0C, 0x00, 0xC1, 0xFC, 0x0C, 0xC0, 0xCC, 0x0C, 0x60, 0x83, 0xF0,
    0x3E, 0x3C, 0x30, 0x60, 0x81, 0x06, 0x0C, 0x18, 0x30, 0x7F, 0x81, 0x06,
    0x0C, 0x18, 0x30, 0x60, 0x81, 0x06, 0x0C, 0x3C, 0x78, 0x1E, 0x18, 0x20,
    0xC1, 0x83, 0x04, 0x18, 0x30, 0x41, 0x87, 0x80, 0x0F, 0x81, 0x80, 0x80,
    0xC0, 0x60, 0x20, 0x30, 0x18, 0x0C, 0x04, 0x36, 0x1E, 0x00, 0x3E, 0x78,
    0x61, 0x82, 0x10, 0x31, 0x01, 0xB0, 0x0E, 0x00, 0x58, 0x06, 0x60, 0x33,
    0x01, 0x0C, 0x18, 0x61, 0xE7, 0xC0, 0x3E, 0x01, 0x80, 0x20, 0x0C, 0x01,
    0x80, 0x30, 0x04, 0x01, 0x80, 0x30, 0x04, 0x0D, 0x83, 0x7F, 0xE0, 0x1C,
    0x07, 0x0C, 0x0E, 0x0C, 0x14, 0x14, 0x1C, 0x14, 0x2C, 0x16, 0x4C, 0x26,
    0x48, 0x26, 0x98, 0x27, 0x18, 0x27, 0x10, 0x42, 0x30, 0xF4, 0x7C, 0x38,
    0x78, 0x60, 0x83, 0x04, 0x2C, 0x41, 0x22, 0x09, 0x10, 0x4D, 0x84, 0x28,
    0x21, 0x41, 0x06, 0x10, 0x21, 0xE1, 0x00, 0x07, 0x83, 0x18, 0xC1, 0xB0,
    0x36, 0x07, 0xC0, 0xF0, 0x3E, 0x06, 0xC0, 0xD8, 0x31, 0x8C, 0x1E, 0x00,
    0x3F, 0xC1, 0x9C, 0x21, 0x8C, 0x31, 0x86, 0x31, 0x87, 0xE1, 0x80, 0x30,
    0x04, 0x01, 0x80, 0x78, 0x00, 0x07, 0x83, 0x18, 0xC1, 0x98, 0x36, 0x07,
    0xC0, 0xF0, 0x1E, 0x06, 0xC0, 0xD8, 0x31, 0x04, 0x13, 0x01, 0x80, 0x70,
    0xB7, 0xE0, 0x3F, 0xC1, 0x8C, 0x21, 0x8C, 0x31, 0x8C, 0x3F, 0x04, 0xC1,
    0x98, 0x31, 0x84, 0x31, 0x86, 0x78, 0x70, 0x1E, 0x4C, 0x63, 0x08, 0xC0,
    0x38, 0x07, 0x00, 0x60, 0x0C, 0x43, 0x10, 0xC6, 0x62, 0x70, 0x7F, 0xE9,
    0x8E, 0x31, 0x04, 0x01, 0x80, 0x30, 0x06, 0x00, 0x80, 0x30, 0x06, 0x00,
    0x80, 0x7E, 0x00, 0x7C, 0xF3, 0x02, 0x30, 0x46, 0x04, 0x60, 0x46, 0x04,
    0x40, 0x8C, 0x08, 0xC0, 0x8C, 0x10, 0xE3, 0x03, 0xC0, 0xF8, 0xEC, 0x0C,
    0x81, 0x18, 0x43, 0x08, 0x62, 0x0C, 0x81, 0x90, 0x14, 0x03, 0x00, 0x60,
    0x08, 0x00, 0xFB, 0xCE, 0x43, 0x0C, 0x86, 0x11, 0x8C, 0x43, 0x38, 0x86,
    0xB2, 0x0D, 0x24, 0x1C, 0x50, 0x38, 0xA0, 0x21, 0x80, 0x42, 0x01, 0x04,
    0x00, 0x3E, 0x71, 0x82, 0x0C, 0x40, 0xC8, 0x07, 0x00, 0x60, 0x06, 0x00,
    0xB0, 0x13, 0x02, 0x18, 0x61, 0x8F, 0x3E, 0xF9, 0xC8, 0x23, 0x10, 0xC8,
    0x34, 0x05, 0x01, 0x80, 0x40, 0x30, 0x0C, 0x03, 0x03, 0xE0, 0x3F, 0xE4,
    0x19, 0x03, 0x00, 0xC0, 0x30, 0x0C, 0x03, 0x00, 0x40, 0x18, 0x06, 0x05,
    0x81, 0x7F, 0xE0, 0x0E, 0x10, 0x20, 0x81, 0x02, 0x04, 0x10, 0x20, 0x40,
    0x82, 0x04, 0x08, 0x1C, 0x00, 0x81, 0x04, 0x18, 0x20, 0xC1, 0x04, 0x08,
    0x20, 0x41, 0x38, 0x20, 0x82, 0x08, 0x41, 0x04, 0x10, 0xC2, 0x08, 0x20,
    0x8C, 0x00, 0x18, 0x18, 0x2C, 0x24, 0x46, 0x42, 0x83, 0xFF, 0x80, 0xD8,
    0x80, 0x1F, 0x98, 0x98, 0x4C, 0x2C, 0x36, 0x33, 0x3A, 0xEE, 0x38, 0x08,
    0x04, 0x02, 0x03, 0x71, 0xCC, 0xC6, 0xC3, 0x63, 0x21, 0x93, 0x8F, 0x00,
    0x1F, 0x33, 0x60, 0xC0, 0xC0, 0xC0, 0xC4, 0x78, 0x01, 0x80, 0x40, 0x60,
    0x20, 0xF1, 0x89, 0x8C, 0xC4, 0xC2, 0x63, 0x33, 0xAE, 0xE0, 0x0E, 0x65,
    0x8B, 0x2F, 0x98, 0x31, 0x3C, 0x01, 0xE0, 0x40, 0x08, 0x02, 0x00, 0x40,
    0x3E, 0x03, 0x00, 0x40, 0x08, 0x01, 0x00, 0x60, 0x0C, 0x01, 0x00, 0x20,
    0x04, 0x01, 0x00, 0xC0, 0x00, 0x1E, 0x19, 0xD8, 0xCC, 0xE1, 0xC3, 0x01,
    0xE0, 0xBC, 0x82, 0x41, 0x31, 0x0F, 0x00, 0x38, 0x08, 0x04, 0x02, 0x03,
    0x39, 0x6C, 0xC6, 0x46, 0x63, 0x21, 0x11, 0xB8, 0xE0, 0x30, 0x00, 0xE2,
    0x44, 0xC8, 0xCE, 0x06, 0x00, 0x00, 0x00, 0xC0, 0x83, 0x04, 0x08, 0x10,
    0x60, 0x81, 0x02, 0x04, 0x70, 0x38, 0x10, 0x10, 0x10, 0x37, 0x22, 0x24,
    0x38, 0x78, 0x48, 0x4D, 0xC6, 0x73, 0x32, 0x26, 0x64, 0x4C, 0xDE, 0x77,
    0x39, 0x5E, 0xCC, 0xCC, 0xCE, 0x66, 0x62, 0x22, 0x11, 0x11, 0xB9, 0x8E,
    0x77, 0x3B, 0x33, 0x62, 0x62, 0x42, 0x4D, 0xCE, 0x0F, 0x18, 0xD8, 0x7C,
    0x3C, 0x3E, 0x1B, 0x18, 0xF0, 0x3B, 0x87, 0x31, 0x8C, 0x43, 0x31, 0x88,
    0x62, 0x30, 0xF0, 0x60, 0x10, 0x04, 0x03, 0x80, 0x0F, 0x18, 0x98, 0x4C,
    0x2C, 0x26, 0x33, 0x38, 0xEC, 0x04, 0x02, 0x03, 0x03, 0xC0, 0x76, 0x50,
    0xC1, 0x06, 0x08, 0x10, 0x60, 0x1A, 0x6C, 0xC8, 0xC0, 0xD1, 0xB3, 0x5C,
    0x23, 0xC8, 0xC4, 0x21, 0x18, 0xE0, 0xC3, 0x42, 0x42, 0xC6, 0x86, 0x8C,
    0x9D, 0xEE, 0x62, 0xC4, 0x89, 0xA3, 0x47, 0x0C, 0x10, 0xE2, 0x2C, 0x44,
    0xD8, 0x9D, 0x23, 0xA4, 0x65, 0x0C, 0xC1, 0x10, 0x19, 0x95, 0x43, 0x01,
    0x80, 0xC0, 0xA0, 0x91, 0x8E, 0x70, 0x88, 0x46, 0x23, 0x20, 0x90, 0x50,
    0x28, 0x18, 0x08, 0x08, 0x08, 0x18, 0x00, 0x3F, 0x42, 0x04, 0x08, 0x10,
    0x20, 0x40, 0x72, 0x0E, 0x08, 0x61, 0x04, 0x30, 0x86, 0x08, 0x61, 0x04,
    0x30, 0xC3, 0x8F, 0x00, 0xFF, 0xF0, 0x1E, 0x0C, 0x10, 0x20, 0xC1, 0x82,
    0x04, 0x1C, 0x30, 0x40, 0x83, 0x04, 0x08, 0x20, 0x60, 0x99, 0x8E};

const GFXglyph FreeSerifItalic9pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 5, 0, 1},        // 0x20 ' '
    {0, 4, 12, 6, 1, -11},     // 0x21 '!'
    {6, 5, 4, 6, 3, -11},      // 0x22 '"'
    {9, 10, 12, 9, 0, -11},    // 0x23 '#'
    {24, 9, 15, 9, 1, -12},    // 0x24 '$'
    {41, 14, 12, 15, 1, -11},  // 0x25 '%'
    {62, 12, 12, 14, 1, -11},  // 0x26 '&'
    {80, 2, 4, 4, 3, -11},     // 0x27 '''
    {81, 6, 15, 6, 1, -11},    // 0x28 '('
    {93, 6, 15, 6, 0, -11},    // 0x29 ')'
    {105, 6, 8, 9, 3, -11},    // 0x2A '*'
    {111, 9, 9, 12, 1, -8},    // 0x2B '+'
    {122, 2, 4, 5, 0, -1},     // 0x2C ','
    {123, 4, 1, 6, 1, -3},     // 0x2D '-'
    {124, 2, 2, 5, 0, -1},     // 0x2E '.'
    {125, 8, 12, 5, 0, -11},   // 0x2F '/'
    {137, 9, 13, 9, 1, -12},   // 0x30 '0'
    {152, 6, 13, 9, 1, -12},   // 0x31 '1'
    {162, 8, 12, 9, 1, -11},   // 0x32 '2'
    {174, 9, 12, 9, 0, -11},   // 0x33 '3'
    {188, 9, 12, 9, 0, -11},   // 0x34 '4'
    {202, 9, 12, 9, 0, -11},   // 0x35 '5'
    {216, 9, 13, 9, 1, -12},   // 0x36 '6'
    {231, 9, 12, 9, 1, -11},   // 0x37 '7'
    {245, 9, 13, 9, 1, -12},   // 0x38 '8'
    {260, 9, 13, 9, 0, -12},   // 0x39 '9'
    {275, 4, 8, 4, 1, -7},     // 0x3A ':'
    {279, 4, 10, 4, 1, -7},    // 0x3B ';'
    {284, 9, 9, 10, 1, -8},    // 0x3C '<'
    {295, 9, 5, 12, 2, -6},    // 0x3D '='
    {301, 9, 9, 10, 1, -8},    // 0x3E '>'
    {312, 7, 12, 8, 2, -11},   // 0x3F '?'
    {323, 13, 12, 14, 1, -11}, // 0x40 '@'
    {343, 11, 11, 12, 0, -10}, // 0x41 'A'
    {359, 11, 12, 11, 0, -11}, // 0x42 'B'
    {376, 12, 12, 11, 1, -11}, // 0x43 'C'
    {394, 13, 12, 13, 0, -11}, // 0x44 'D'
    {414, 12, 12, 10, 0, -11}, // 0x45 'E'
    {432, 12, 12, 10, 0, -11}, // 0x46 'F'
    {450, 12, 12, 12, 1, -11}, // 0x47 'G'
    {468, 14, 12, 13, 0, -11}, // 0x48 'H'
    {489, 7, 12, 6, 0, -11},   // 0x49 'I'
    {500, 9, 12, 8, 0, -11},   // 0x4A 'J'
    {514, 13, 12, 12, 0, -11}, // 0x4B 'K'
    {534, 11, 12, 10, 0, -11}, // 0x4C 'L'
    {551, 16, 12, 15, 0, -11}, // 0x4D 'M'
    {575, 13, 12, 12, 0, -11}, // 0x4E 'N'
    {595, 11, 12, 12, 1, -11}, // 0x4F 'O'
    {612, 11, 12, 10, 0, -11}, // 0x50 'P'
    {629, 11, 15, 12, 1, -11}, // 0x51 'Q'
    {650, 11, 12, 11, 0, -11}, // 0x52 'R'
    {667, 10, 12, 8, 0, -11},  // 0x53 'S'
    {682, 11, 12, 11, 2, -11}, // 0x54 'T'
    {699, 12, 12, 13, 2, -11}, // 0x55 'U'
    {717, 11, 12, 12, 2, -11}, // 0x56 'V'
    {734, 15, 12, 16, 2, -11}, // 0x57 'W'
    {757, 12, 12, 12, 0, -11}, // 0x58 'X'
    {775, 10, 12, 11, 2, -11}, // 0x59 'Y'
    {790, 11, 12, 10, 0, -11}, // 0x5A 'Z'
    {807, 7, 15, 7, 0, -11},   // 0x5B '['
    {821, 6, 12, 9, 2, -11},   // 0x5C '\'
    {830, 6, 15, 7, 1, -11},   // 0x5D ']'
    {842, 8, 7, 8, 0, -11},    // 0x5E '^'
    {849, 9, 1, 9, 0, 2},      // 0x5F '_'
    {851, 3, 3, 5, 2, -11},    // 0x60 '`'
    {853, 9, 8, 9, 0, -7},     // 0x61 'a'
    {862, 9, 12, 9, 0, -11},   // 0x62 'b'
    {876, 8, 8, 7, 0, -7},     // 0x63 'c'
    {884, 9, 12, 9, 0, -11},   // 0x64 'd'
    {898, 7, 8, 7, 0, -7},     // 0x65 'e'
    {905, 11, 17, 8, -1, -12}, // 0x66 'f'
    {929, 9, 12, 8, 0, -7},    // 0x67 'g'
    {943, 9, 12, 9, 0, -11},   // 0x68 'h'
    {957, 4, 12, 4, 1, -11},   // 0x69 'i'
    {963, 7, 16, 5, -1, -11},  // 0x6A 'j'
    {977, 8, 12, 8, 0, -11},   // 0x6B 'k'
    {989, 4, 12, 5, 1, -11},   // 0x6C 'l'
    {995, 13, 8, 13, 0, -7},   // 0x6D 'm'
    {1008, 8, 8, 9, 0, -7},    // 0x6E 'n'
    {1016, 9, 8, 9, 0, -7},    // 0x6F 'o'
    {1025, 10, 12, 8, -1, -7}, // 0x70 'p'
    {1040, 9, 12, 9, 0, -7},   // 0x71 'q'
    {1054, 7, 8, 7, 0, -7},    // 0x72 'r'
    {1061, 7, 8, 6, 0, -7},    // 0x73 's'
    {1068, 5, 9, 4, 0, -8},    // 0x74 't'
    {1074, 8, 8, 9, 1, -7},    // 0x75 'u'
    {1082, 7, 8, 8, 1, -7},    // 0x76 'v'
    {1089, 11, 8, 12, 1, -7},  // 0x77 'w'
    {1100, 9, 8, 8, -1, -7},   // 0x78 'x'
    {1109, 9, 12, 9, 0, -7},   // 0x79 'y'
    {1123, 8, 9, 7, 0, -7},    // 0x7A 'z'
    {1132, 6, 15, 7, 1, -11},  // 0x7B '{'
    {1144, 1, 12, 5, 2, -11},  // 0x7C '|'
    {1146, 7, 16, 7, 0, -12},  // 0x7D '}'
    {1160, 8, 3, 10, 1, -5}};  // 0x7E '~'

const GFXfont FreeSerifItalic9pt7b PROGMEM = {
    (uint8_t *)FreeSerifItalic9pt7bBitmaps,
    (GFXglyph *)FreeSerifItalic9pt7bGlyphs, 0x20, 0x7E, 22};

// Approx. 1835 bytes
