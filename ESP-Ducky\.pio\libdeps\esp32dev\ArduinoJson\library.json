{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keywords": "json, rest, http, web", "description": "A simple and efficient JSON library for embedded C++. ArduinoJson supports ✔ serialization, ✔ deserialization, ✔ MessagePack, ✔ fixed allocation, ✔ zero-copy, ✔ streams, ✔ filtering, and more. It is the most popular Arduino library on GitHub ❤❤❤❤❤. Check out arduinojson.org for a comprehensive documentation.", "homepage": "https://arduinojson.org/?utm_source=meta&utm_medium=library.json", "repository": {"type": "git", "url": "https://github.com/bblanchon/ArduinoJson.git"}, "version": "6.21.5", "authors": {"name": "<PERSON><PERSON>", "url": "https://blog.benoitblanchon.fr"}, "export": {"include": ["src", "examples", "LICENSE.txt", "ArduinoJson.h"]}, "frameworks": "*", "platforms": "*", "build": {"libArchive": false}}